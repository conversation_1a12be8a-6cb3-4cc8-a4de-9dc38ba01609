import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumFilterToolbar extends StatefulWidget {
  final String selectedFilter;
  final Function(String) onFilterSelected;
  final double brightness;
  final double contrast;
  final double saturation;
  final ValueChanged<double> onBrightnessChanged;
  final ValueChanged<double> onContrastChanged;
  final ValueChanged<double> onSaturationChanged;
  final VoidCallback onClose;

  const PremiumFilterToolbar({
    super.key,
    required this.selectedFilter,
    required this.onFilterSelected,
    required this.brightness,
    required this.contrast,
    required this.saturation,
    required this.onBrightnessChanged,
    required this.onContrastChanged,
    required this.onSaturationChanged,
    required this.onClose,
  });

  @override
  State<PremiumFilterToolbar> createState() => _PremiumFilterToolbarState();
}

class _PremiumFilterToolbarState extends State<PremiumFilterToolbar>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  
  bool _showAdjustments = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startEntryAnimation();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: AppTheme.curveEmphasized,
    ));
  }

  void _startEntryAnimation() {
    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          decoration: BoxDecoration(
            color: AppTheme.glassColor,
            border: Border.all(
              color: AppTheme.glassBorderColor,
              width: 1,
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(AppTheme.radius2XL),
              topRight: Radius.circular(AppTheme.radius2XL),
            ),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle
                Container(
                  margin: const EdgeInsets.only(top: AppTheme.spacing12),
                  width: AppTheme.spacing40,
                  height: AppTheme.spacing4,
                  decoration: BoxDecoration(
                    color: AppTheme.glassBorderColor,
                    borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                  ),
                ),
                
                // Header
                _buildHeader(),
                
                // Content
                if (_showAdjustments)
                  _buildAdjustments()
                else
                  _buildFilters(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacing20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            _showAdjustments ? 'Adjustments' : 'Filters',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.textOnPrimaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          Row(
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    _showAdjustments = !_showAdjustments;
                  });
                  HapticFeedback.lightImpact();
                },
                child: Container(
                  padding: const EdgeInsets.all(AppTheme.spacing8),
                  decoration: BoxDecoration(
                    color: _showAdjustments 
                        ? AppTheme.brandPrimary.withValues(alpha: 0.3)
                        : AppTheme.glassColor,
                    borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                    border: Border.all(
                      color: AppTheme.glassBorderColor,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.tune_rounded,
                    color: _showAdjustments 
                        ? AppTheme.brandPrimary
                        : AppTheme.textOnPrimaryColor,
                    size: AppTheme.spacing16,
                  ),
                ),
              ),
              const SizedBox(width: AppTheme.spacing12),
              GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  widget.onClose();
                },
                child: Container(
                  padding: const EdgeInsets.all(AppTheme.spacing8),
                  decoration: BoxDecoration(
                    color: AppTheme.glassColor,
                    borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                    border: Border.all(
                      color: AppTheme.glassBorderColor,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.close_rounded,
                    color: AppTheme.textOnPrimaryColor,
                    size: AppTheme.spacing16,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    final filters = [
      {'key': 'original', 'name': 'Original'},
      {'key': 'auto', 'name': 'Auto'},
      {'key': 'bw', 'name': 'B&W'},
      {'key': 'sepia', 'name': 'Sepia'},
      {'key': 'vintage', 'name': 'Vintage'},
      {'key': 'cool', 'name': 'Cool'},
      {'key': 'warm', 'name': 'Warm'},
      {'key': 'vivid', 'name': 'Vivid'},
    ];

    return Container(
      height: AppTheme.spacing96,
      padding: const EdgeInsets.only(bottom: AppTheme.spacing20),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacing20),
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = widget.selectedFilter == filter['key'];
          
          return Container(
            margin: const EdgeInsets.only(right: AppTheme.spacing12),
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                widget.onFilterSelected(filter['key'] as String);
              },
              child: Column(
                children: [
                  Container(
                    width: AppTheme.spacing56,
                    height: AppTheme.spacing56,
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? AppTheme.brandPrimary.withValues(alpha: 0.3)
                          : AppTheme.glassColor,
                      borderRadius: BorderRadius.circular(AppTheme.radiusMD),
                      border: Border.all(
                        color: isSelected 
                            ? AppTheme.brandPrimary
                            : AppTheme.glassBorderColor,
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      _getFilterIcon(filter['key'] as String),
                      color: isSelected 
                          ? AppTheme.brandPrimary
                          : AppTheme.textOnPrimaryColor,
                      size: AppTheme.spacing24,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacing8),
                  Text(
                    filter['name'] as String,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isSelected 
                          ? AppTheme.brandPrimary
                          : AppTheme.textOnPrimaryColor.withValues(alpha: 0.7),
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAdjustments() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacing20),
      child: Column(
        children: [
          _buildSlider(
            label: 'Brightness',
            value: widget.brightness,
            onChanged: widget.onBrightnessChanged,
            icon: Icons.brightness_6_rounded,
          ),
          const SizedBox(height: AppTheme.spacing20),
          _buildSlider(
            label: 'Contrast',
            value: widget.contrast,
            onChanged: widget.onContrastChanged,
            icon: Icons.contrast_rounded,
          ),
          const SizedBox(height: AppTheme.spacing20),
          _buildSlider(
            label: 'Saturation',
            value: widget.saturation,
            onChanged: widget.onSaturationChanged,
            icon: Icons.palette_rounded,
          ),
        ],
      ),
    );
  }

  Widget _buildSlider({
    required String label,
    required double value,
    required ValueChanged<double> onChanged,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: AppTheme.textOnPrimaryColor,
              size: AppTheme.spacing16,
            ),
            const SizedBox(width: AppTheme.spacing8),
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textOnPrimaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            Text(
              '${(value * 100).round()}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacing8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: AppTheme.brandPrimary,
            inactiveTrackColor: AppTheme.glassBorderColor,
            thumbColor: AppTheme.brandPrimary,
            overlayColor: AppTheme.brandPrimary.withValues(alpha: 0.2),
            trackHeight: 4,
          ),
          child: Slider(
            value: value,
            min: -1.0,
            max: 1.0,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  IconData _getFilterIcon(String filter) {
    switch (filter) {
      case 'auto':
        return Icons.auto_fix_high_rounded;
      case 'bw':
        return Icons.filter_b_and_w_rounded;
      case 'sepia':
        return Icons.filter_vintage_rounded;
      case 'vintage':
        return Icons.filter_frames_rounded;
      case 'cool':
        return Icons.ac_unit_rounded;
      case 'warm':
        return Icons.wb_sunny_rounded;
      case 'vivid':
        return Icons.colorize_rounded;
      case 'original':
      default:
        return Icons.image_outlined;
    }
  }
}

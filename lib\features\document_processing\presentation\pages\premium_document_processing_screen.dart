import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';
import '../widgets/premium_image_editor.dart';
import '../widgets/premium_filter_toolbar.dart';
import '../widgets/premium_processing_controls.dart';

class PremiumDocumentProcessingScreen extends StatefulWidget {
  final String imagePath;

  const PremiumDocumentProcessingScreen({super.key, required this.imagePath});

  @override
  State<PremiumDocumentProcessingScreen> createState() =>
      _PremiumDocumentProcessingScreenState();
}

class _PremiumDocumentProcessingScreenState
    extends State<PremiumDocumentProcessingScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  String _selectedFilter = 'original';
  bool _isProcessing = false;
  bool _showFilters = false;
  double _brightness = 0.0;
  double _contrast = 0.0;
  double _saturation = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startEntryAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _slideController = AnimationController(
      duration: AppTheme.animationSlow,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: AppTheme.curveEmphasized),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1.0),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _slideController,
        curve: AppTheme.curveEmphasized,
      ),
    );
  }

  void _startEntryAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _slideController.forward();
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _toggleFilters() {
    setState(() {
      _showFilters = !_showFilters;
    });
    HapticFeedback.lightImpact();
  }

  void _applyFilter(String filter) {
    setState(() {
      _selectedFilter = filter;
    });
    HapticFeedback.lightImpact();
  }

  void _updateBrightness(double value) {
    setState(() {
      _brightness = value;
    });
  }

  void _updateContrast(double value) {
    setState(() {
      _contrast = value;
    });
  }

  void _updateSaturation(double value) {
    setState(() {
      _saturation = value;
    });
  }

  Future<void> _saveDocument() async {
    setState(() {
      _isProcessing = true;
    });

    HapticFeedback.mediumImpact();

    // Simulate processing
    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      setState(() {
        _isProcessing = false;
      });

      // Navigate to file manager or show success
      Navigator.of(context).pushReplacementNamed('/file-manager');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryDarkColor,
      body: AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: Stack(
              children: [
                // Main Content
                Column(
                  children: [
                    // App Bar
                    _buildAppBar(),

                    // Image Editor
                    Expanded(
                      child: PremiumImageEditor(
                        imagePath: widget.imagePath,
                        selectedFilter: _selectedFilter,
                        brightness: _brightness,
                        contrast: _contrast,
                        saturation: _saturation,
                      ),
                    ),
                  ],
                ),

                // Filter Toolbar
                if (_showFilters)
                  PremiumFilterToolbar(
                    selectedFilter: _selectedFilter,
                    onFilterSelected: _applyFilter,
                    brightness: _brightness,
                    contrast: _contrast,
                    saturation: _saturation,
                    onBrightnessChanged: _updateBrightness,
                    onContrastChanged: _updateContrast,
                    onSaturationChanged: _updateSaturation,
                    onClose: _toggleFilters,
                  ),

                // Bottom Controls
                AnimatedBuilder(
                  animation: _slideAnimation,
                  builder: (context, child) {
                    return SlideTransition(
                      position: _slideAnimation,
                      child: PremiumProcessingControls(
                        isProcessing: _isProcessing,
                        onFiltersToggle: _toggleFilters,
                        onSave: _saveDocument,
                        onCancel: () => Navigator.of(context).pop(),
                        showFilters: _showFilters,
                      ),
                    );
                  },
                ),

                // Processing Overlay
                if (_isProcessing) _buildProcessingOverlay(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAppBar() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacing20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                Navigator.of(context).pop();
              },
              child: Container(
                padding: const EdgeInsets.all(AppTheme.spacing12),
                decoration: BoxDecoration(
                  color: AppTheme.glassColor,
                  borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                  border: Border.all(
                    color: AppTheme.glassBorderColor,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryDarkColor.withValues(alpha: 0.5),
                      blurRadius: AppTheme.shadowBlurMD,
                      offset: const Offset(0, AppTheme.spacing4),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.arrow_back_rounded,
                  color: AppTheme.textOnPrimaryColor,
                  size: AppTheme.spacing20,
                ),
              ),
            ),

            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing16,
                vertical: AppTheme.spacing8,
              ),
              decoration: BoxDecoration(
                color: AppTheme.glassColor,
                borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                border: Border.all(color: AppTheme.glassBorderColor, width: 1),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryDarkColor.withValues(alpha: 0.5),
                    blurRadius: AppTheme.shadowBlurMD,
                    offset: const Offset(0, AppTheme.spacing4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.auto_fix_high_rounded,
                    color: AppTheme.brandPrimary,
                    size: AppTheme.spacing16,
                  ),
                  const SizedBox(width: AppTheme.spacing8),
                  Text(
                    'Enhance',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textOnPrimaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

            GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                // Show more options
              },
              child: Container(
                padding: const EdgeInsets.all(AppTheme.spacing12),
                decoration: BoxDecoration(
                  color: AppTheme.glassColor,
                  borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                  border: Border.all(
                    color: AppTheme.glassBorderColor,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryDarkColor.withValues(alpha: 0.5),
                      blurRadius: AppTheme.shadowBlurMD,
                      offset: const Offset(0, AppTheme.spacing4),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.more_vert_rounded,
                  color: AppTheme.textOnPrimaryColor,
                  size: AppTheme.spacing20,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProcessingOverlay() {
    return Container(
      color: AppTheme.primaryDarkColor.withValues(alpha: 0.8),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(AppTheme.spacing32),
          decoration: BoxDecoration(
            color: AppTheme.glassColor,
            borderRadius: BorderRadius.circular(AppTheme.radius2XL),
            border: Border.all(color: AppTheme.glassBorderColor, width: 1),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryDarkColor.withValues(alpha: 0.5),
                blurRadius: AppTheme.shadowBlur2XL,
                offset: const Offset(0, AppTheme.spacing16),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: AppTheme.spacing64,
                height: AppTheme.spacing64,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: AppTheme.primaryGradient,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(AppTheme.radius2XL),
                ),
                child: Center(
                  child: SizedBox(
                    width: AppTheme.spacing32,
                    height: AppTheme.spacing32,
                    child: CircularProgressIndicator(
                      strokeWidth: 3,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.textOnPrimaryColor,
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: AppTheme.spacing24),

              Text(
                'Processing Document...',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppTheme.textOnPrimaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),

              const SizedBox(height: AppTheme.spacing8),

              Text(
                'Applying filters and enhancements',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

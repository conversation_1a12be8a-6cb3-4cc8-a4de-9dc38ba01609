import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';

class EnhancedSettingsTile extends StatefulWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final Widget? trailing;
  final VoidCallback? onTap;
  final bool enabled;
  final Color? iconColor;

  const EnhancedSettingsTile({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    this.trailing,
    this.onTap,
    this.enabled = true,
    this.iconColor,
  });

  factory EnhancedSettingsTile.switchTile({
    required String title,
    String? subtitle,
    required IconData icon,
    required bool value,
    required ValueChanged<bool> onChanged,
    bool enabled = true,
    Color? iconColor,
  }) {
    return EnhancedSettingsTile(
      title: title,
      subtitle: subtitle,
      icon: icon,
      enabled: enabled,
      iconColor: iconColor,
      trailing: Switch.adaptive(
        value: value,
        onChanged: enabled ? onChanged : null,
        activeColor: AppTheme.primaryColor,
      ),
      onTap: enabled ? () => onChanged(!value) : null,
    );
  }

  factory EnhancedSettingsTile.navigation({
    required String title,
    String? subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool enabled = true,
    Color? iconColor,
  }) {
    return EnhancedSettingsTile(
      title: title,
      subtitle: subtitle,
      icon: icon,
      enabled: enabled,
      iconColor: iconColor,
      trailing: Icon(
        Icons.chevron_right_rounded,
        color: enabled ? AppTheme.textSecondaryColor : AppTheme.textTertiaryColor,
        size: AppTheme.spacing24,
      ),
      onTap: enabled ? onTap : null,
    );
  }

  @override
  State<EnhancedSettingsTile> createState() => _EnhancedSettingsTileState();
}

class _EnhancedSettingsTileState extends State<EnhancedSettingsTile>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppTheme.animationFast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppTheme.curveStandard,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.onTap != null && widget.enabled) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.onTap != null && widget.enabled) {
      setState(() => _isPressed = false);
      _animationController.reverse();
      widget.onTap!();
    }
  }

  void _onTapCancel() {
    if (widget.onTap != null && widget.enabled) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: AppTheme.spacing4),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusLG),
              border: Border.all(
                color: _isPressed
                    ? AppTheme.primaryColor.withValues(alpha: 0.2)
                    : AppTheme.outlineColor,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.textPrimaryColor.withValues(alpha: 0.02),
                  blurRadius: AppTheme.spacing8,
                  offset: const Offset(0, AppTheme.spacing2),
                ),
                if (_isPressed)
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    blurRadius: AppTheme.spacing12,
                    offset: const Offset(0, AppTheme.spacing4),
                  ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                onTap: widget.enabled ? widget.onTap : null,
                onTapDown: _onTapDown,
                onTapUp: _onTapUp,
                onTapCancel: _onTapCancel,
                child: Padding(
                  padding: const EdgeInsets.all(AppTheme.spacing20),
                  child: Row(
                    children: [
                      // Icon container
                      Container(
                        width: AppTheme.spacing48,
                        height: AppTheme.spacing48,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              (widget.iconColor ?? AppTheme.primaryColor)
                                  .withValues(alpha: 0.15),
                              (widget.iconColor ?? AppTheme.primaryColor)
                                  .withValues(alpha: 0.05),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(AppTheme.radiusMD),
                        ),
                        child: Icon(
                          widget.icon,
                          color: widget.enabled
                              ? (widget.iconColor ?? AppTheme.primaryColor)
                              : AppTheme.textTertiaryColor,
                          size: AppTheme.spacing24,
                        ),
                      ),
                      
                      const SizedBox(width: AppTheme.spacing16),
                      
                      // Content
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.title,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: widget.enabled
                                    ? AppTheme.textPrimaryColor
                                    : AppTheme.textTertiaryColor,
                              ),
                            ),
                            if (widget.subtitle != null) ...[
                              const SizedBox(height: AppTheme.spacing4),
                              Text(
                                widget.subtitle!,
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: widget.enabled
                                      ? AppTheme.textSecondaryColor
                                      : AppTheme.textTertiaryColor,
                                  height: 1.3,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      
                      // Trailing widget
                      if (widget.trailing != null) ...[
                        const SizedBox(width: AppTheme.spacing12),
                        widget.trailing!,
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class EnhancedSettingsSection extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final EdgeInsetsGeometry? padding;

  const EnhancedSettingsSection({
    super.key,
    required this.title,
    required this.children,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.symmetric(vertical: AppTheme.spacing8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacing4,
              vertical: AppTheme.spacing8,
            ),
            child: Text(
              title.toUpperCase(),
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.8,
              ),
            ),
          ),
          const SizedBox(height: AppTheme.spacing8),
          ...children,
        ],
      ),
    );
  }
}

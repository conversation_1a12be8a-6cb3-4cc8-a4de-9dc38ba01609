import 'package:equatable/equatable.dart';
import 'package:camera/camera.dart';
import 'dart:typed_data';

abstract class CameraState extends Equatable {
  const CameraState();

  @override
  List<Object?> get props => [];
}

class <PERSON><PERSON>nitial extends CameraState {}

class CameraLoading extends CameraState {}

class <PERSON>Ready extends CameraState {
  final CameraController controller;
  final bool isFlashOn;

  const CameraReady({
    required this.controller,
    this.isFlashOn = false,
  });

  @override
  List<Object?> get props => [controller, isFlashOn];

  CameraReady copyWith({
    CameraController? controller,
    bool? isFlashOn,
  }) {
    return CameraReady(
      controller: controller ?? this.controller,
      isFlashOn: isFlashOn ?? this.isFlashOn,
    );
  }
}

class CameraCapturing extends CameraState {
  final CameraController controller;

  const CameraCapturing({required this.controller});

  @override
  List<Object?> get props => [controller];
}

class CameraImageCaptured extends CameraState {
  final Uint8List imageBytes;
  final CameraController controller;

  const CameraImageCaptured({
    required this.imageBytes,
    required this.controller,
  });

  @override
  List<Object?> get props => [imageBytes, controller];
}

class CameraError extends CameraState {
  final String message;

  const CameraError({required this.message});

  @override
  List<Object?> get props => [message];
}

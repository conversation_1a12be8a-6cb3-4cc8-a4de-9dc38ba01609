import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:camera/camera.dart';
import '../../domain/usecases/initialize_camera.dart';
import '../../domain/usecases/capture_image.dart';
import 'camera_event.dart';
import 'camera_state.dart';

class CameraBloc extends Bloc<CameraEvent, CameraState> {
  final InitializeCamera initializeCamera;
  final CaptureImage captureImage;
  
  CameraController? _controller;

  CameraBloc({
    required this.initializeCamera,
    required this.captureImage,
  }) : super(CameraInitial()) {
    on<InitializeCameraEvent>(_onInitializeCamera);
    on<CaptureImageEvent>(_onCaptureImage);
    on<DisposeCameraEvent>(_onDisposeCamera);
    on<ToggleFlashEvent>(_onToggleFlash);
  }

  Future<void> _onInitializeCamera(
    InitializeCameraEvent event,
    Emitter<CameraState> emit,
  ) async {
    try {
      emit(CameraLoading());
      
      _controller = await initializeCamera();
      
      emit(CameraReady(controller: _controller!));
    } catch (e) {
      emit(CameraError(message: e.toString()));
    }
  }

  Future<void> _onCaptureImage(
    CaptureImageEvent event,
    Emitter<CameraState> emit,
  ) async {
    if (_controller == null || !_controller!.value.isInitialized) {
      emit(const CameraError(message: 'Camera not initialized'));
      return;
    }

    try {
      emit(CameraCapturing(controller: _controller!));
      
      final imageBytes = await captureImage(_controller!);
      
      emit(CameraImageCaptured(
        imageBytes: imageBytes,
        controller: _controller!,
      ));
    } catch (e) {
      emit(CameraError(message: e.toString()));
    }
  }

  Future<void> _onDisposeCamera(
    DisposeCameraEvent event,
    Emitter<CameraState> emit,
  ) async {
    if (_controller != null) {
      await _controller!.dispose();
      _controller = null;
    }
    emit(CameraInitial());
  }

  Future<void> _onToggleFlash(
    ToggleFlashEvent event,
    Emitter<CameraState> emit,
  ) async {
    if (_controller == null || !_controller!.value.isInitialized) {
      return;
    }

    try {
      final currentState = state;
      if (currentState is CameraReady) {
        final newFlashMode = currentState.isFlashOn ? FlashMode.off : FlashMode.torch;
        await _controller!.setFlashMode(newFlashMode);
        
        emit(currentState.copyWith(isFlashOn: !currentState.isFlashOn));
      }
    } catch (e) {
      emit(CameraError(message: e.toString()));
    }
  }

  @override
  Future<void> close() {
    if (_controller != null) {
      _controller!.dispose();
    }
    return super.close();
  }
}

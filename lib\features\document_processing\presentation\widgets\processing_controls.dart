import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

class ProcessingControls extends StatelessWidget {
  final VoidCallback onCrop;
  final VoidCallback onRetake;
  final VoidCallback onEnhance;
  final bool canCrop;
  final bool canEnhance;

  const ProcessingControls({
    super.key,
    required this.onCrop,
    required this.onRetake,
    required this.onEnhance,
    this.canCrop = false,
    this.canEnhance = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: const BoxDecoration(
        color: Colors.black87,
        border: Border(
          top: BorderSide(color: Colors.white24, width: 1),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Instructions
          Text(
            canCrop 
                ? 'Adjust the corners to fit your document, then tap Crop'
                : canEnhance
                    ? 'Document cropped successfully! You can enhance or continue.'
                    : 'Processing your document...',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Control buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Retake button
              _buildControlButton(
                icon: Icons.camera_alt,
                label: 'Retake',
                onPressed: onRetake,
                color: Colors.grey,
              ),
              
              // Crop button
              _buildControlButton(
                icon: Icons.crop,
                label: 'Crop',
                onPressed: canCrop ? onCrop : null,
                color: Colors.blue,
              ),
              
              // Enhance button
              _buildControlButton(
                icon: Icons.auto_fix_high,
                label: 'Enhance',
                onPressed: canEnhance ? onEnhance : null,
                color: Colors.green,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    required Color color,
  }) {
    final isEnabled = onPressed != null;
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isEnabled ? color : Colors.grey.shade600,
            border: Border.all(
              color: Colors.white24,
              width: 1,
            ),
          ),
          child: IconButton(
            onPressed: onPressed,
            icon: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            color: isEnabled ? Colors.white : Colors.grey,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}

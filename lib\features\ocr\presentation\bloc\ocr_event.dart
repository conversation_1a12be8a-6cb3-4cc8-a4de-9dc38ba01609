import 'package:equatable/equatable.dart';
import 'dart:typed_data';

abstract class OcrEvent extends Equatable {
  const OcrEvent();

  @override
  List<Object?> get props => [];
}

class ExtractTextEvent extends OcrEvent {
  final Uint8List imageBytes;

  const ExtractTextEvent({required this.imageBytes});

  @override
  List<Object?> get props => [imageBytes];
}

class ExtractStructuredTextEvent extends OcrEvent {
  final Uint8List imageBytes;

  const ExtractStructuredTextEvent({required this.imageBytes});

  @override
  List<Object?> get props => [imageBytes];
}

class ClearOcrResultsEvent extends OcrEvent {}

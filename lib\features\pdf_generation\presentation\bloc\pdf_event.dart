import 'package:equatable/equatable.dart';
import 'dart:typed_data';

abstract class PdfEvent extends Equatable {
  const PdfEvent();

  @override
  List<Object?> get props => [];
}

class GeneratePdfEvent extends PdfEvent {
  final List<Uint8List> images;
  final String fileName;

  const GeneratePdfEvent({
    required this.images,
    required this.fileName,
  });

  @override
  List<Object?> get props => [images, fileName];
}

class SharePdfEvent extends PdfEvent {
  final String filePath;

  const SharePdfEvent({required this.filePath});

  @override
  List<Object?> get props => [filePath];
}

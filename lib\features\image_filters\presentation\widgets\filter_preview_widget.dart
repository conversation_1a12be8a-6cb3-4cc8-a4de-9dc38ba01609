import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../domain/repositories/image_filter_repository.dart';

class FilterPreviewWidget extends StatelessWidget {
  final FilterType filterType;
  final String name;
  final IconData icon;
  final bool isSelected;
  final VoidCallback onTap;

  const FilterPreviewWidget({
    super.key,
    required this.filterType,
    required this.name,
    required this.icon,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected 
              ? AppTheme.primaryColor.withValues(alpha: 0.1)
              : AppTheme.surfaceVariantColor,
          borderRadius: BorderRadius.circular(AppTheme.radiusSM),
          border: Border.all(
            color: isSelected 
                ? AppTheme.primaryColor 
                : AppTheme.outlineColor,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacing8),
              decoration: BoxDecoration(
                color: isSelected 
                    ? AppTheme.primaryColor 
                    : AppTheme.textSecondaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusSM),
              ),
              child: Icon(
                icon,
                color: isSelected 
                    ? AppTheme.textOnPrimaryColor 
                    : AppTheme.textSecondaryColor,
                size: AppConstants.iconSizeMD,
              ),
            ),
            const SizedBox(height: AppTheme.spacing8),
            Text(
              name,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: isSelected 
                    ? AppTheme.primaryColor 
                    : AppTheme.textSecondaryColor,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

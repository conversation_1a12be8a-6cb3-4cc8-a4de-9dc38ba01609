import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumStatsCards extends StatefulWidget {
  const PremiumStatsCards({super.key});

  @override
  State<PremiumStatsCards> createState() => _PremiumStatsCardsState();
}

class _PremiumStatsCardsState extends State<PremiumStatsCards>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startStaggeredAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      3,
      (index) => AnimationController(
        duration: AppTheme.animationNormal,
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: controller,
          curve: AppTheme.curveEmphasized,
        ),
      );
    }).toList();
  }

  void _startStaggeredAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: 100 * i), () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Activity',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppTheme.spacing16),
        Row(
          children: [
            Expanded(
              child: AnimatedBuilder(
                animation: _animations[0],
                builder: (context, child) {
                  return Transform.scale(
                    scale: _animations[0].value,
                    child: Opacity(
                      opacity: _animations[0].value,
                      child: _buildStatsCard(
                        title: 'Documents',
                        value: '24',
                        subtitle: 'This month',
                        icon: Icons.description_outlined,
                        gradient: AppTheme.primaryGradient,
                        trend: '+12%',
                        isPositive: true,
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: AppTheme.spacing12),
            Expanded(
              child: AnimatedBuilder(
                animation: _animations[1],
                builder: (context, child) {
                  return Transform.scale(
                    scale: _animations[1].value,
                    child: Opacity(
                      opacity: _animations[1].value,
                      child: _buildStatsCard(
                        title: 'Storage',
                        value: '2.4GB',
                        subtitle: 'Used',
                        icon: Icons.cloud_outlined,
                        gradient: AppTheme.secondaryGradient,
                        trend: '68%',
                        isPositive: false,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacing12),
        AnimatedBuilder(
          animation: _animations[2],
          builder: (context, child) {
            return Transform.scale(
              scale: _animations[2].value,
              child: Opacity(
                opacity: _animations[2].value,
                child: _buildWideStatsCard(),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildStatsCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required List<Color> gradient,
    required String trend,
    required bool isPositive,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radius2XL),
        border: Border.all(
          color: AppTheme.outlineColor,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
            blurRadius: AppTheme.shadowBlurLG,
            offset: const Offset(0, AppTheme.spacing8),
          ),
          BoxShadow(
            color: AppTheme.textPrimaryColor.withValues(alpha: 0.02),
            blurRadius: AppTheme.shadowBlur2XL,
            offset: const Offset(0, AppTheme.spacing16),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacing10),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: gradient,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMD),
                ),
                child: Icon(
                  icon,
                  color: AppTheme.textOnPrimaryColor,
                  size: AppTheme.spacing20,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing8,
                  vertical: AppTheme.spacing4,
                ),
                decoration: BoxDecoration(
                  color: isPositive
                      ? AppTheme.successColor.withValues(alpha: 0.1)
                      : AppTheme.warningColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                ),
                child: Text(
                  trend,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: isPositive ? AppTheme.successColor : AppTheme.warningColor,
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacing16),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacing4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textTertiaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWideStatsCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacing24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: AppTheme.accentGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radius2XL),
        boxShadow: [
          BoxShadow(
            color: AppTheme.accentColor.withValues(alpha: 0.3),
            blurRadius: AppTheme.shadowBlurLG,
            offset: const Offset(0, AppTheme.spacing8),
          ),
          BoxShadow(
            color: AppTheme.accentColor.withValues(alpha: 0.1),
            blurRadius: AppTheme.shadowBlur2XL,
            offset: const Offset(0, AppTheme.spacing16),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Productivity Score',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppTheme.textOnPrimaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppTheme.spacing8),
                Row(
                  children: [
                    Text(
                      '94%',
                      style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                        color: AppTheme.textOnPrimaryColor,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacing8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacing8,
                        vertical: AppTheme.spacing4,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.glassColor,
                        borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                        border: Border.all(
                          color: AppTheme.glassBorderColor,
                          width: 1,
                        ),
                      ),
                      child: Text(
                        'Excellent',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textOnPrimaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.spacing4),
                Text(
                  'Based on scan quality and frequency',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: AppTheme.spacing56,
            height: AppTheme.spacing56,
            decoration: BoxDecoration(
              color: AppTheme.glassColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusLG),
              border: Border.all(
                color: AppTheme.glassBorderColor,
                width: 1,
              ),
            ),
            child: Icon(
              Icons.trending_up_rounded,
              color: AppTheme.textOnPrimaryColor,
              size: AppTheme.spacing28,
            ),
          ),
        ],
      ),
    );
  }
}

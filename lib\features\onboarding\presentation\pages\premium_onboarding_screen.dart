import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumOnboardingScreen extends StatefulWidget {
  const PremiumOnboardingScreen({super.key});

  @override
  State<PremiumOnboardingScreen> createState() => _PremiumOnboardingScreenState();
}

class _PremiumOnboardingScreenState extends State<PremiumOnboardingScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  int _currentPage = 0;
  final int _totalPages = 3;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _startEntryAnimation();
  }

  void _initializeControllers() {
    _pageController = PageController();
    _fadeController = AnimationController(
      duration: AppTheme.animationSlow,
      vsync: this,
    );
    _slideController = AnimationController(
      duration: AppTheme.animationSlower,
      vsync: this,
    );
  }

  void _initializeAnimations() {
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: AppTheme.curveEmphasized,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: AppTheme.curveEmphasized,
    ));
  }

  void _startEntryAnimation() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _slideController.forward();
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: AppTheme.animationNormal,
        curve: AppTheme.curveEmphasized,
      );
    } else {
      _completeOnboarding();
    }
    HapticFeedback.lightImpact();
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: AppTheme.animationNormal,
        curve: AppTheme.curveEmphasized,
      );
    }
    HapticFeedback.lightImpact();
  }

  void _completeOnboarding() {
    HapticFeedback.mediumImpact();
    Navigator.of(context).pushReplacementNamed('/home');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: AnimatedBuilder(
        animation: Listenable.merge([_fadeAnimation, _slideAnimation]),
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: SafeArea(
                child: Column(
                  children: [
                    // Header
                    _buildHeader(),
                    
                    // Page Content
                    Expanded(
                      child: PageView(
                        controller: _pageController,
                        onPageChanged: (index) {
                          setState(() {
                            _currentPage = index;
                          });
                        },
                        children: [
                          _buildPage1(),
                          _buildPage2(),
                          _buildPage3(),
                        ],
                      ),
                    ),
                    
                    // Bottom Navigation
                    _buildBottomNavigation(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacing24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Logo
          Row(
            children: [
              Container(
                width: AppTheme.spacing40,
                height: AppTheme.spacing40,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: AppTheme.primaryGradient,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMD),
                ),
                child: Icon(
                  Icons.document_scanner_rounded,
                  color: AppTheme.textOnPrimaryColor,
                  size: AppTheme.spacing20,
                ),
              ),
              const SizedBox(width: AppTheme.spacing12),
              Text(
                'LensDoc',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),
          
          // Skip Button
          if (_currentPage < _totalPages - 1)
            TextButton(
              onPressed: _completeOnboarding,
              child: Text(
                'Skip',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPage1() {
    return _buildOnboardingPage(
      gradient: AppTheme.primaryGradient,
      icon: Icons.document_scanner_rounded,
      title: 'Professional Document Scanning',
      subtitle: 'Transform any document into high-quality digital files with advanced AI-powered scanning technology.',
      features: [
        'AI-powered edge detection',
        'Auto enhancement filters',
        'Multi-page document support',
      ],
    );
  }

  Widget _buildPage2() {
    return _buildOnboardingPage(
      gradient: AppTheme.secondaryGradient,
      icon: Icons.auto_fix_high_rounded,
      title: 'Smart Enhancement & OCR',
      subtitle: 'Extract text from documents and apply intelligent filters for crystal-clear results.',
      features: [
        'Advanced OCR text recognition',
        'Smart image enhancement',
        'Multiple export formats',
      ],
    );
  }

  Widget _buildPage3() {
    return _buildOnboardingPage(
      gradient: AppTheme.accentGradient,
      icon: Icons.cloud_sync_rounded,
      title: 'Organize & Share',
      subtitle: 'Keep your documents organized and share them seamlessly across all your devices.',
      features: [
        'Smart file organization',
        'Cloud synchronization',
        'Easy sharing options',
      ],
    );
  }

  Widget _buildOnboardingPage({
    required List<Color> gradient,
    required IconData icon,
    required String title,
    required String subtitle,
    required List<String> features,
  }) {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacing24),
      child: Column(
        children: [
          const SizedBox(height: AppTheme.spacing32),
          
          // Hero Icon
          Container(
            width: AppTheme.spacing128,
            height: AppTheme.spacing128,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: gradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(AppTheme.radius3XL),
              boxShadow: [
                BoxShadow(
                  color: gradient.first.withValues(alpha: 0.3),
                  blurRadius: AppTheme.shadowBlur2XL,
                  offset: const Offset(0, AppTheme.spacing16),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: AppTheme.textOnPrimaryColor,
              size: AppTheme.spacing64,
            ),
          ),
          
          const SizedBox(height: AppTheme.spacing48),
          
          // Title
          Text(
            title,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppTheme.spacing16),
          
          // Subtitle
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppTheme.spacing40),
          
          // Features
          Column(
            children: features.map((feature) {
              return Container(
                margin: const EdgeInsets.only(bottom: AppTheme.spacing16),
                padding: const EdgeInsets.all(AppTheme.spacing16),
                decoration: BoxDecoration(
                  color: AppTheme.surfaceColor,
                  borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                  border: Border.all(
                    color: AppTheme.outlineColor,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
                      blurRadius: AppTheme.shadowBlurMD,
                      offset: const Offset(0, AppTheme.spacing4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(AppTheme.spacing6),
                      decoration: BoxDecoration(
                        color: gradient.first.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                      ),
                      child: Icon(
                        Icons.check_rounded,
                        color: gradient.first,
                        size: AppTheme.spacing16,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacing16),
                    Expanded(
                      child: Text(
                        feature,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textPrimaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacing24),
      child: Column(
        children: [
          // Page Indicators
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(_totalPages, (index) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: AppTheme.spacing4),
                width: _currentPage == index ? AppTheme.spacing24 : AppTheme.spacing8,
                height: AppTheme.spacing8,
                decoration: BoxDecoration(
                  color: _currentPage == index 
                      ? AppTheme.brandPrimary
                      : AppTheme.textTertiaryColor,
                  borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                ),
              );
            }),
          ),
          
          const SizedBox(height: AppTheme.spacing32),
          
          // Navigation Buttons
          Row(
            children: [
              if (_currentPage > 0)
                Expanded(
                  child: _buildNavigationButton(
                    label: 'Previous',
                    onPressed: _previousPage,
                    isPrimary: false,
                  ),
                ),
              
              if (_currentPage > 0)
                const SizedBox(width: AppTheme.spacing16),
              
              Expanded(
                child: _buildNavigationButton(
                  label: _currentPage == _totalPages - 1 ? 'Get Started' : 'Next',
                  onPressed: _nextPage,
                  isPrimary: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButton({
    required String label,
    required VoidCallback onPressed,
    required bool isPrimary,
  }) {
    return Container(
      height: AppTheme.spacing56,
      decoration: BoxDecoration(
        gradient: isPrimary 
            ? LinearGradient(
                colors: AppTheme.primaryGradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: isPrimary ? null : AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radius2XL),
        border: isPrimary 
            ? null 
            : Border.all(
                color: AppTheme.outlineColor,
                width: 1,
              ),
        boxShadow: [
          if (isPrimary)
            BoxShadow(
              color: AppTheme.brandPrimary.withValues(alpha: 0.3),
              blurRadius: AppTheme.shadowBlurLG,
              offset: const Offset(0, AppTheme.spacing8),
            ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppTheme.radius2XL),
          onTap: onPressed,
          child: Center(
            child: Text(
              label,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: isPrimary 
                    ? AppTheme.textOnPrimaryColor
                    : AppTheme.textPrimaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

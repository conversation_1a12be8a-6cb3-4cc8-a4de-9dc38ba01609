import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';

class PreviewControls extends StatelessWidget {
  final VoidCallback onSavePdf;
  final VoidCallback onAddPage;
  final VoidCallback? onShare;
  final bool isGenerating;
  final bool canShare;

  const PreviewControls({
    super.key,
    required this.onSavePdf,
    required this.onAddPage,
    this.onShare,
    this.isGenerating = false,
    this.canShare = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        border: Border(
          top: BorderSide(
            color: AppTheme.outlineColor,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.textPrimaryColor.withValues(alpha: 0.05),
            blurRadius: AppTheme.spacing8,
            offset: const Offset(0, -AppTheme.spacing4),
          ),
        ],
      ),
      child: Safe<PERSON>rea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Primary actions row
            Row(
              children: [
                // Add page button
                Expanded(
                  child: _buildSecondaryButton(
                    icon: Icons.add_photo_alternate_outlined,
                    label: 'Add Page',
                    onPressed: isGenerating ? null : onAddPage,
                  ),
                ),
                
                const SizedBox(width: AppTheme.spacing12),
                
                // Save PDF button
                Expanded(
                  flex: 2,
                  child: _buildPrimaryButton(
                    icon: isGenerating ? null : Icons.picture_as_pdf_outlined,
                    label: isGenerating ? 'Generating...' : 'Save PDF',
                    onPressed: isGenerating ? null : onSavePdf,
                    isLoading: isGenerating,
                  ),
                ),
              ],
            ),
            
            // Secondary actions row
            if (canShare) ...[
              const SizedBox(height: AppTheme.spacing12),
              Row(
                children: [
                  // Share button
                  Expanded(
                    child: _buildSecondaryButton(
                      icon: Icons.share_outlined,
                      label: 'Share',
                      onPressed: onShare,
                    ),
                  ),
                  
                  const SizedBox(width: AppTheme.spacing12),
                  
                  // More options button
                  Expanded(
                    child: _buildSecondaryButton(
                      icon: Icons.more_horiz_outlined,
                      label: 'More',
                      onPressed: () => _showMoreOptions(context),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPrimaryButton({
    IconData? icon,
    required String label,
    required VoidCallback? onPressed,
    bool isLoading = false,
  }) {
    return SizedBox(
      height: AppConstants.buttonHeight,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: AppTheme.textOnPrimaryColor,
          elevation: AppTheme.elevation2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusSM),
          ),
        ),
        child: isLoading
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: AppTheme.spacing20,
                    height: AppTheme.spacing20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.textOnPrimaryColor,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacing12),
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeBase,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (icon != null) ...[
                    Icon(
                      icon,
                      size: AppConstants.iconSizeMD,
                    ),
                    const SizedBox(width: AppTheme.spacing8),
                  ],
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeBase,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildSecondaryButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
  }) {
    return SizedBox(
      height: AppConstants.buttonHeight,
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          foregroundColor: AppTheme.primaryColor,
          side: BorderSide(
            color: AppTheme.primaryColor,
            width: 1.5,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusSM),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: AppConstants.iconSizeMD,
            ),
            const SizedBox(width: AppTheme.spacing8),
            Text(
              label,
              style: TextStyle(
                fontSize: AppTheme.fontSizeSM,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.surfaceColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppTheme.radiusLG),
        ),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppTheme.spacing24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.outlineColor,
                borderRadius: BorderRadius.circular(AppTheme.radiusRound),
              ),
            ),
            
            const SizedBox(height: AppTheme.spacing24),
            
            // Title
            Text(
              'More Options',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: AppTheme.spacing24),
            
            // Options
            _buildOptionTile(
              context,
              icon: Icons.email_outlined,
              title: 'Send via Email',
              subtitle: 'Share document via email',
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Implement email sharing
              },
            ),
            
            _buildOptionTile(
              context,
              icon: Icons.cloud_upload_outlined,
              title: 'Upload to Cloud',
              subtitle: 'Save to cloud storage',
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Implement cloud upload
              },
            ),
            
            _buildOptionTile(
              context,
              icon: Icons.print_outlined,
              title: 'Print Document',
              subtitle: 'Send to printer',
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Implement printing
              },
            ),
            
            _buildOptionTile(
              context,
              icon: Icons.copy_outlined,
              title: 'Duplicate',
              subtitle: 'Create a copy of this document',
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Implement duplication
              },
            ),
            
            const SizedBox(height: AppTheme.spacing16),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(AppTheme.spacing8),
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusSM),
        ),
        child: Icon(
          icon,
          color: AppTheme.primaryColor,
          size: AppConstants.iconSizeMD,
        ),
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: AppTheme.textSecondaryColor,
        ),
      ),
      onTap: onTap,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusSM),
      ),
    );
  }
}

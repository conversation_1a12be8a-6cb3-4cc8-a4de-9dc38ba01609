import 'package:flutter/material.dart';
import 'dart:typed_data';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';

class DocumentPageWidget extends StatelessWidget {
  final Uint8List imageBytes;
  final int pageIndex;
  final int totalPages;
  final VoidCallback? onDelete;
  final Function(int)? onReorder;

  const DocumentPageWidget({
    super.key,
    required this.imageBytes,
    required this.pageIndex,
    required this.totalPages,
    this.onDelete,
    this.onReorder,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacing16),
      child: Column(
        children: [
          // Page controls
          if (totalPages > 1)
            Container(
              margin: const EdgeInsets.only(bottom: AppTheme.spacing12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Move left button
                  IconButton(
                    onPressed:
                        pageIndex > 0
                            ? () => onReorder?.call(pageIndex - 1)
                            : null,
                    icon: Icon(
                      Icons.keyboard_arrow_left,
                      color:
                          pageIndex > 0
                              ? AppTheme.primaryColor
                              : AppTheme.textTertiaryColor,
                    ),
                    tooltip: 'Move left',
                  ),

                  // Page indicator
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacing16,
                      vertical: AppTheme.spacing8,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                      border: Border.all(
                        color: AppTheme.primaryColor.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      'Page ${pageIndex + 1} of $totalPages',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                  // Move right button
                  IconButton(
                    onPressed:
                        pageIndex < totalPages - 1
                            ? () => onReorder?.call(pageIndex + 1)
                            : null,
                    icon: Icon(
                      Icons.keyboard_arrow_right,
                      color:
                          pageIndex < totalPages - 1
                              ? AppTheme.primaryColor
                              : AppTheme.textTertiaryColor,
                    ),
                    tooltip: 'Move right',
                  ),
                ],
              ),
            ),

          // Document image
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppTheme.surfaceColor,
                borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                border: Border.all(color: AppTheme.outlineColor, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.textPrimaryColor.withValues(alpha: 0.1),
                    blurRadius: AppTheme.spacing16,
                    offset: const Offset(0, AppTheme.spacing8),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppTheme.radiusLG - 2),
                child: Stack(
                  children: [
                    // Document image
                    Positioned.fill(
                      child: Image.memory(imageBytes, fit: BoxFit.contain),
                    ),

                    // Action buttons overlay
                    Positioned(
                      top: AppTheme.spacing12,
                      right: AppTheme.spacing12,
                      child: Column(
                        children: [
                          // Edit button
                          _buildActionButton(
                            icon: Icons.edit_outlined,
                            onPressed: () => _editPage(context),
                            backgroundColor: AppTheme.primaryColor,
                            tooltip: 'Edit page',
                          ),

                          const SizedBox(height: AppTheme.spacing8),

                          // Enhance button
                          _buildActionButton(
                            icon: Icons.auto_fix_high_outlined,
                            onPressed: () => _enhancePage(context),
                            backgroundColor: AppTheme.secondaryColor,
                            tooltip: 'Enhance',
                          ),

                          if (onDelete != null) ...[
                            const SizedBox(height: AppTheme.spacing8),

                            // Delete button
                            _buildActionButton(
                              icon: Icons.delete_outline,
                              onPressed: () => _confirmDelete(context),
                              backgroundColor: AppTheme.errorColor,
                              tooltip: 'Delete page',
                            ),
                          ],
                        ],
                      ),
                    ),

                    // Page number badge
                    Positioned(
                      bottom: AppTheme.spacing12,
                      left: AppTheme.spacing12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacing12,
                          vertical: AppTheme.spacing6,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.textPrimaryColor.withValues(
                            alpha: 0.8,
                          ),
                          borderRadius: BorderRadius.circular(
                            AppTheme.radiusRound,
                          ),
                        ),
                        child: Text(
                          '${pageIndex + 1}',
                          style: Theme.of(
                            context,
                          ).textTheme.labelMedium?.copyWith(
                            color: AppTheme.textOnPrimaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color backgroundColor,
    required String tooltip,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusRound),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withValues(alpha: 0.3),
            blurRadius: AppTheme.spacing8,
            offset: const Offset(0, AppTheme.spacing4),
          ),
        ],
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: AppTheme.textOnPrimaryColor,
          size: AppConstants.iconSizeMD,
        ),
        tooltip: tooltip,
        constraints: const BoxConstraints(minWidth: 44, minHeight: 44),
      ),
    );
  }

  void _editPage(BuildContext context) {
    Navigator.of(context).pushNamed('/image-editor', arguments: imageBytes);
  }

  void _enhancePage(BuildContext context) {
    Navigator.of(context).pushNamed('/image-editor', arguments: imageBytes);
  }

  void _confirmDelete(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Page'),
            content: Text(
              'Are you sure you want to delete page ${pageIndex + 1}? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onDelete?.call();
                },
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.errorColor,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }
}

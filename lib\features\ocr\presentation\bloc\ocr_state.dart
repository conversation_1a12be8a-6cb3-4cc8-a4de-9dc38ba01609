import 'package:equatable/equatable.dart';

abstract class OcrState extends Equatable {
  const OcrState();

  @override
  List<Object?> get props => [];
}

class OcrInitial extends OcrState {}

class OcrLoading extends OcrState {}

class OcrTextExtracted extends OcrState {
  final String extractedText;

  const OcrTextExtracted({required this.extractedText});

  @override
  List<Object?> get props => [extractedText];
}

class OcrStructuredTextExtracted extends OcrState {
  final Map<String, dynamic> structuredData;

  const OcrStructuredTextExtracted({required this.structuredData});

  @override
  List<Object?> get props => [structuredData];
}

class OcrError extends OcrState {
  final String message;

  const OcrError({required this.message});

  @override
  List<Object?> get props => [message];
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumBottomNavigation extends StatefulWidget {
  final int currentIndex;
  final ValueChanged<int> onTap;

  const PremiumBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  State<PremiumBottomNavigation> createState() =>
      _PremiumBottomNavigationState();
}

class _PremiumBottomNavigationState extends State<PremiumBottomNavigation>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<AnimationController> _iconControllers;
  late List<Animation<double>> _iconAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _iconControllers = List.generate(3, (index) {
      return AnimationController(duration: AppTheme.animationFast, vsync: this);
    });

    _iconAnimations =
        _iconControllers.map((controller) {
          return Tween<double>(begin: 1.0, end: 1.2).animate(
            CurvedAnimation(parent: controller, curve: AppTheme.curveStandard),
          );
        }).toList();

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    for (final controller in _iconControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _onItemTap(int index) {
    // Handle center button (index 1) differently
    if (index == 1) {
      // Center scan button - always trigger action
      widget.onTap(index);
      return;
    }

    // For other tabs, only animate if not currently selected
    if (index != widget.currentIndex) {
      final actualIndex =
          index > 1 ? index - 1 : index; // Adjust for missing Files tab
      if (actualIndex < _iconControllers.length) {
        _iconControllers[actualIndex].forward().then((_) {
          _iconControllers[actualIndex].reverse();
        });
      }
    }
    widget.onTap(index);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Bottom Navigation Bar
        Container(
          decoration: BoxDecoration(
            color: AppTheme.glassColor,
            border: Border(
              top: BorderSide(color: AppTheme.glassBorderColor, width: 1),
            ),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryDarkColor.withValues(alpha: 0.1),
                blurRadius: AppTheme.shadowBlurLG,
                offset: const Offset(0, -AppTheme.spacing4),
              ),
            ],
          ),
          child: SafeArea(
            child: Container(
              height: AppTheme.spacing64,
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing20,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildNavItem(
                    index: 0,
                    icon: Icons.home_rounded,
                    label: 'Home',
                  ),
                  // Empty space for center button
                  const SizedBox(width: AppTheme.spacing56),
                  _buildNavItem(
                    index: 2,
                    icon: Icons.settings_rounded,
                    label: 'Settings',
                  ),
                ],
              ),
            ),
          ),
        ),

        // Docked Floating Action Button
        Positioned(
          left: 0,
          right: 0,
          top: -AppTheme.spacing28,
          child: Center(child: _buildDockedScanButton()),
        ),
      ],
    );
  }

  Widget _buildNavItem({
    required int index,
    required IconData icon,
    required String label,
  }) {
    final isSelected = widget.currentIndex == index;
    final animationIndex =
        index == 2 ? 1 : index; // Map index 2 to animation index 1

    return AnimatedBuilder(
      animation: _iconAnimations[animationIndex],
      builder: (context, child) {
        return GestureDetector(
          onTap: () => _onItemTap(index),
          child: Transform.scale(
            scale: _iconAnimations[animationIndex].value,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing16,
                vertical: AppTheme.spacing8,
              ),
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? AppTheme.brandPrimary.withValues(alpha: 0.1)
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                border:
                    isSelected
                        ? Border.all(
                          color: AppTheme.brandPrimary.withValues(alpha: 0.3),
                          width: 1,
                        )
                        : null,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    icon,
                    color:
                        isSelected
                            ? AppTheme.brandPrimary
                            : AppTheme.textSecondaryColor,
                    size: AppTheme.spacing20,
                  ),
                  const SizedBox(height: AppTheme.spacing2),
                  Text(
                    label,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color:
                          isSelected
                              ? AppTheme.brandPrimary
                              : AppTheme.textSecondaryColor,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w500,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDockedScanButton() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.mediumImpact();
        widget.onTap(1); // Center scan button index
      },
      child: Container(
        width: AppTheme.spacing64,
        height: AppTheme.spacing64,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: AppTheme.primaryGradient,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(AppTheme.radius2XL),
          border: Border.all(color: AppTheme.backgroundColor, width: 3),
          boxShadow: [
            BoxShadow(
              color: AppTheme.brandPrimary.withValues(alpha: 0.4),
              blurRadius: AppTheme.shadowBlurXL,
              offset: const Offset(0, AppTheme.spacing8),
            ),
            BoxShadow(
              color: AppTheme.primaryDarkColor.withValues(alpha: 0.2),
              blurRadius: AppTheme.shadowBlurLG,
              offset: const Offset(0, AppTheme.spacing4),
            ),
          ],
        ),
        child: Icon(
          Icons.camera_alt_rounded,
          color: AppTheme.textOnPrimaryColor,
          size: AppTheme.spacing32,
        ),
      ),
    );
  }
}

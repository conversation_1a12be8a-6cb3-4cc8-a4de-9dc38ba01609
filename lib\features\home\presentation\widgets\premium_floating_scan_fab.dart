import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumFloatingScanFAB extends StatefulWidget {
  const PremiumFloatingScanFAB({super.key});

  @override
  State<PremiumFloatingScanFAB> createState() => _PremiumFloatingScanFABState();
}

class _PremiumFloatingScanFABState extends State<PremiumFloatingScanFAB>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _rotationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _scaleController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: AppTheme.curveEmphasized,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.125, // 45 degrees
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: AppTheme.curveStandard,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _scaleController.forward();
      _rotationController.forward();
    } else {
      _scaleController.reverse();
      _rotationController.reverse();
    }

    HapticFeedback.lightImpact();
  }

  void _handleQuickAction(String action) {
    _toggleExpanded();
    HapticFeedback.mediumImpact();
    
    switch (action) {
      case 'camera':
        Navigator.of(context).pushNamed('/camera');
        break;
      case 'gallery':
        // Import from gallery
        break;
      case 'files':
        Navigator.of(context).pushNamed('/file-manager');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        // Backdrop
        if (_isExpanded)
          GestureDetector(
            onTap: _toggleExpanded,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: AppTheme.textPrimaryColor.withValues(alpha: 0.1),
            ),
          ),

        // Quick Action Buttons
        AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Opacity(
                    opacity: _scaleAnimation.value,
                    child: Column(
                      children: [
                        _buildQuickActionButton(
                          icon: Icons.folder_outlined,
                          label: 'Files',
                          gradient: AppTheme.primaryGradient,
                          onTap: () => _handleQuickAction('files'),
                        ),
                        const SizedBox(height: AppTheme.spacing12),
                        _buildQuickActionButton(
                          icon: Icons.photo_library_outlined,
                          label: 'Gallery',
                          gradient: AppTheme.secondaryGradient,
                          onTap: () => _handleQuickAction('gallery'),
                        ),
                        const SizedBox(height: AppTheme.spacing12),
                        _buildQuickActionButton(
                          icon: Icons.camera_alt_outlined,
                          label: 'Camera',
                          gradient: AppTheme.accentGradient,
                          onTap: () => _handleQuickAction('camera'),
                        ),
                        const SizedBox(height: AppTheme.spacing20),
                      ],
                    ),
                  ),
                ),

                // Main FAB
                _buildMainFAB(),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required List<Color> gradient,
    required VoidCallback onTap,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Label
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacing12,
            vertical: AppTheme.spacing8,
          ),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusMD),
            border: Border.all(
              color: AppTheme.outlineColor,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppTheme.textPrimaryColor.withValues(alpha: 0.1),
                blurRadius: AppTheme.shadowBlurMD,
                offset: const Offset(0, AppTheme.spacing4),
              ),
            ],
          ),
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ),
        
        const SizedBox(width: AppTheme.spacing12),
        
        // Button
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: AppTheme.spacing48,
            height: AppTheme.spacing48,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: gradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(AppTheme.radiusLG),
              boxShadow: [
                BoxShadow(
                  color: gradient.first.withValues(alpha: 0.4),
                  blurRadius: AppTheme.shadowBlurMD,
                  offset: const Offset(0, AppTheme.spacing4),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: AppTheme.textOnPrimaryColor,
              size: AppTheme.spacing20,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMainFAB() {
    return GestureDetector(
      onTap: _toggleExpanded,
      child: AnimatedBuilder(
        animation: _rotationAnimation,
        builder: (context, child) {
          return Transform.rotate(
            angle: _rotationAnimation.value * 2 * 3.14159,
            child: Container(
              width: AppTheme.spacing64,
              height: AppTheme.spacing64,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: AppTheme.primaryGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(AppTheme.radius2XL),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.brandPrimary.withValues(alpha: 0.4),
                    blurRadius: AppTheme.shadowBlurLG,
                    offset: const Offset(0, AppTheme.spacing8),
                  ),
                  BoxShadow(
                    color: AppTheme.brandPrimary.withValues(alpha: 0.2),
                    blurRadius: AppTheme.shadowBlur2XL,
                    offset: const Offset(0, AppTheme.spacing16),
                  ),
                ],
              ),
              child: Container(
                margin: const EdgeInsets.all(AppTheme.spacing4),
                decoration: BoxDecoration(
                  color: AppTheme.glassColor,
                  borderRadius: BorderRadius.circular(AppTheme.radiusXL),
                  border: Border.all(
                    color: AppTheme.glassBorderColor,
                    width: 1,
                  ),
                ),
                child: Icon(
                  _isExpanded ? Icons.close_rounded : Icons.add_rounded,
                  color: AppTheme.textOnPrimaryColor,
                  size: AppTheme.spacing28,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

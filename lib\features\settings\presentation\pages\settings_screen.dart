import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../widgets/settings_section.dart';
import '../widgets/settings_tile.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _autoEnhanceEnabled = true;
  bool _ocrEnabled = true;
  bool _cloudSyncEnabled = false;
  bool _darkModeEnabled = false;
  String _defaultFormat = 'PDF';
  String _imageQuality = 'High';
  String _language = 'English';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Settings',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppTheme.spacing16),
        children: [
          // Scanning Settings
          SettingsSection(
            title: 'Scanning',
            children: [
              SettingsTile.switchTile(
                title: 'Auto Enhancement',
                subtitle: 'Automatically enhance scanned documents',
                value: _autoEnhanceEnabled,
                onChanged: (value) {
                  setState(() {
                    _autoEnhanceEnabled = value;
                  });
                },
                icon: Icons.auto_fix_high_outlined,
              ),
              SettingsTile.switchTile(
                title: 'OCR Text Recognition',
                subtitle: 'Extract text from scanned documents',
                value: _ocrEnabled,
                onChanged: (value) {
                  setState(() {
                    _ocrEnabled = value;
                  });
                },
                icon: Icons.text_fields_outlined,
              ),
              SettingsTile.navigation(
                title: 'Image Quality',
                subtitle: _imageQuality,
                icon: Icons.high_quality_outlined,
                onTap: () => _showImageQualityDialog(),
              ),
              SettingsTile.navigation(
                title: 'Default Export Format',
                subtitle: _defaultFormat,
                icon: Icons.file_download_outlined,
                onTap: () => _showFormatDialog(),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacing24),

          // Cloud & Sync Settings
          SettingsSection(
            title: 'Cloud & Sync',
            children: [
              SettingsTile.switchTile(
                title: 'Cloud Sync',
                subtitle: 'Sync documents across devices',
                value: _cloudSyncEnabled,
                onChanged: (value) {
                  setState(() {
                    _cloudSyncEnabled = value;
                  });
                  if (value) {
                    _showCloudSyncDialog();
                  }
                },
                icon: Icons.cloud_sync_outlined,
              ),
              SettingsTile.navigation(
                title: 'Storage Usage',
                subtitle: 'Manage local and cloud storage',
                icon: Icons.storage_outlined,
                onTap: () => _showStorageDialog(),
              ),
              SettingsTile.navigation(
                title: 'Backup & Restore',
                subtitle: 'Backup your documents',
                icon: Icons.backup_outlined,
                onTap: () => _showBackupDialog(),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacing24),

          // App Settings
          SettingsSection(
            title: 'App Settings',
            children: [
              SettingsTile.switchTile(
                title: 'Dark Mode',
                subtitle: 'Use dark theme',
                value: _darkModeEnabled,
                onChanged: (value) {
                  setState(() {
                    _darkModeEnabled = value;
                  });
                },
                icon: Icons.dark_mode_outlined,
              ),
              SettingsTile.navigation(
                title: 'Language',
                subtitle: _language,
                icon: Icons.language_outlined,
                onTap: () => _showLanguageDialog(),
              ),
              SettingsTile.navigation(
                title: 'Notifications',
                subtitle: 'Manage notification preferences',
                icon: Icons.notifications_outlined,
                onTap: () => _showNotificationSettings(),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacing24),

          // About & Support
          SettingsSection(
            title: 'About & Support',
            children: [
              SettingsTile.navigation(
                title: 'Help & FAQ',
                subtitle: 'Get help and find answers',
                icon: Icons.help_outline,
                onTap: () => _showHelp(),
              ),
              SettingsTile.navigation(
                title: 'Privacy Policy',
                subtitle: 'Read our privacy policy',
                icon: Icons.privacy_tip_outlined,
                onTap: () => _showPrivacyPolicy(),
              ),
              SettingsTile.navigation(
                title: 'Terms of Service',
                subtitle: 'Read terms and conditions',
                icon: Icons.description_outlined,
                onTap: () => _showTerms(),
              ),
              SettingsTile.navigation(
                title: 'About LensDoc',
                subtitle: 'Version 1.0.0',
                icon: Icons.info_outline,
                onTap: () => _showAbout(),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacing32),

          // Reset Settings
          Container(
            padding: const EdgeInsets.all(AppTheme.spacing16),
            decoration: BoxDecoration(
              color: AppTheme.errorColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusMD),
              border: Border.all(
                color: AppTheme.errorColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.warning_outlined,
                  color: AppTheme.errorColor,
                  size: AppConstants.iconSizeLG,
                ),
                const SizedBox(height: AppTheme.spacing8),
                Text(
                  'Reset Settings',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppTheme.errorColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppTheme.spacing4),
                Text(
                  'Reset all settings to default values',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppTheme.spacing16),
                OutlinedButton(
                  onPressed: _showResetDialog,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.errorColor,
                    side: BorderSide(color: AppTheme.errorColor),
                  ),
                  child: const Text('Reset All Settings'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showImageQualityDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Image Quality'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('High (Recommended)'),
              subtitle: const Text('Best quality, larger file size'),
              value: 'High',
              groupValue: _imageQuality,
              onChanged: (value) {
                setState(() {
                  _imageQuality = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('Medium'),
              subtitle: const Text('Good quality, moderate file size'),
              value: 'Medium',
              groupValue: _imageQuality,
              onChanged: (value) {
                setState(() {
                  _imageQuality = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('Low'),
              subtitle: const Text('Lower quality, smaller file size'),
              value: 'Low',
              groupValue: _imageQuality,
              onChanged: (value) {
                setState(() {
                  _imageQuality = value!;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showFormatDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Default Export Format'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('PDF'),
              subtitle: const Text('Portable Document Format'),
              value: 'PDF',
              groupValue: _defaultFormat,
              onChanged: (value) {
                setState(() {
                  _defaultFormat = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('JPEG'),
              subtitle: const Text('Image format'),
              value: 'JPEG',
              groupValue: _defaultFormat,
              onChanged: (value) {
                setState(() {
                  _defaultFormat = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('PNG'),
              subtitle: const Text('Lossless image format'),
              value: 'PNG',
              groupValue: _defaultFormat,
              onChanged: (value) {
                setState(() {
                  _defaultFormat = value!;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCloudSyncDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cloud Sync'),
        content: const Text(
          'Cloud sync feature is coming soon! You\'ll be able to sync your documents across all your devices.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showStorageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Storage Usage'),
        content: const Text('Storage management feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Backup & Restore'),
        content: const Text('Backup feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Language'),
        content: const Text('Multiple language support coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notifications'),
        content: const Text('Notification settings coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help & FAQ'),
        content: const Text('Help documentation coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Policy'),
        content: const Text('Privacy policy coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showTerms() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Terms of Service'),
        content: const Text('Terms of service coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showAbout() {
    showAboutDialog(
      context: context,
      applicationName: 'LensDoc',
      applicationVersion: '1.0.0',
      applicationIcon: Icon(
        Icons.document_scanner_outlined,
        size: AppConstants.iconSizeXL,
        color: AppTheme.primaryColor,
      ),
      children: [
        const Text('Professional document scanning made simple.'),
        const SizedBox(height: AppTheme.spacing8),
        const Text('Built with Flutter and powered by ML Kit.'),
      ],
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text(
          'Are you sure you want to reset all settings to their default values? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetAllSettings();
            },
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.errorColor,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _resetAllSettings() {
    setState(() {
      _autoEnhanceEnabled = true;
      _ocrEnabled = true;
      _cloudSyncEnabled = false;
      _darkModeEnabled = false;
      _defaultFormat = 'PDF';
      _imageQuality = 'High';
      _language = 'English';
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Settings reset to default values'),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.radiusSM),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';

class EnhancedSearchBar extends StatefulWidget {
  final Function(String) onSearchChanged;
  final String hintText;
  final bool autofocus;

  const EnhancedSearchBar({
    super.key,
    required this.onSearchChanged,
    this.hintText = 'Search...',
    this.autofocus = false,
  });

  @override
  State<EnhancedSearchBar> createState() => _EnhancedSearchBarState();
}

class _EnhancedSearchBarState extends State<EnhancedSearchBar>
    with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _focusNode = FocusNode();
    _animationController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppTheme.curveStandard,
    ));

    _controller.addListener(() {
      final hasText = _controller.text.isNotEmpty;
      if (hasText != _hasText) {
        setState(() {
          _hasText = hasText;
        });
      }
      widget.onSearchChanged(_controller.text);
    });

    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing16,
        vertical: AppTheme.spacing8,
      ),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.surfaceElevatedColor,
                borderRadius: BorderRadius.circular(AppTheme.radiusXL),
                border: Border.all(
                  color: _focusNode.hasFocus
                      ? AppTheme.primaryColor.withValues(alpha: 0.3)
                      : AppTheme.outlineColor,
                  width: 1.5,
                ),
                boxShadow: [
                  if (_focusNode.hasFocus)
                    BoxShadow(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      blurRadius: AppTheme.spacing16,
                      offset: const Offset(0, AppTheme.spacing4),
                    ),
                  BoxShadow(
                    color: AppTheme.textPrimaryColor.withValues(alpha: 0.03),
                    blurRadius: AppTheme.spacing8,
                    offset: const Offset(0, AppTheme.spacing2),
                  ),
                ],
              ),
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                autofocus: widget.autofocus,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppTheme.textPrimaryColor,
                ),
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppTheme.textTertiaryColor,
                  ),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.only(
                      left: AppTheme.spacing20,
                      right: AppTheme.spacing12,
                    ),
                    child: Icon(
                      Icons.search_outlined,
                      color: _focusNode.hasFocus
                          ? AppTheme.primaryColor
                          : AppTheme.textTertiaryColor,
                      size: AppTheme.spacing24,
                    ),
                  ),
                  suffixIcon: _hasText
                      ? Padding(
                          padding: const EdgeInsets.only(right: AppTheme.spacing8),
                          child: IconButton(
                            onPressed: () {
                              _controller.clear();
                              _focusNode.unfocus();
                            },
                            icon: Icon(
                              Icons.clear_rounded,
                              color: AppTheme.textSecondaryColor,
                              size: AppTheme.spacing20,
                            ),
                            tooltip: 'Clear search',
                          ),
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacing20,
                    vertical: AppTheme.spacing16,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

import 'package:equatable/equatable.dart';
import 'dart:typed_data';
import 'package:flutter/material.dart';

abstract class DocumentProcessingEvent extends Equatable {
  const DocumentProcessingEvent();

  @override
  List<Object?> get props => [];
}

class ProcessImageEvent extends DocumentProcessingEvent {
  final Uint8List imageBytes;

  const ProcessImageEvent({required this.imageBytes});

  @override
  List<Object?> get props => [imageBytes];
}

class DetectEdgesEvent extends DocumentProcessingEvent {
  final Uint8List imageBytes;

  const DetectEdgesEvent({required this.imageBytes});

  @override
  List<Object?> get props => [imageBytes];
}

class CropImageEvent extends DocumentProcessingEvent {
  final Uint8List imageBytes;
  final List<Offset> corners;

  const CropImageEvent({
    required this.imageBytes,
    required this.corners,
  });

  @override
  List<Object?> get props => [imageBytes, corners];
}

class UpdateCornersEvent extends DocumentProcessingEvent {
  final List<Offset> corners;

  const UpdateCornersEvent({required this.corners});

  @override
  List<Object?> get props => [corners];
}

class EnhanceImageEvent extends DocumentProcessingEvent {
  final Uint8List imageBytes;

  const EnhanceImageEvent({required this.imageBytes});

  @override
  List<Object?> get props => [imageBytes];
}

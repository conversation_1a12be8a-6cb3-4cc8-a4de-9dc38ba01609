import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';

class FileManagerHeader extends StatelessWidget {
  final int totalPdfs;
  final int totalImages;
  final bool isGridView;
  final VoidCallback onViewToggle;
  final VoidCallback onRefresh;

  const FileManagerHeader({
    super.key,
    required this.totalPdfs,
    required this.totalImages,
    required this.isGridView,
    required this.onViewToggle,
    required this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        border: Border(
          bottom: BorderSide(
            color: AppTheme.outlineColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and actions
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'My Documents',
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacing4),
                    Text(
                      'Manage your scanned documents',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Action buttons
              Row(
                children: [
                  // View toggle button
                  Container(
                    decoration: BoxDecoration(
                      color: AppTheme.surfaceVariantColor,
                      borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                    ),
                    child: IconButton(
                      onPressed: onViewToggle,
                      icon: Icon(
                        isGridView ? Icons.view_list_outlined : Icons.grid_view_outlined,
                        color: AppTheme.primaryColor,
                      ),
                      tooltip: isGridView ? 'List view' : 'Grid view',
                    ),
                  ),
                  
                  const SizedBox(width: AppTheme.spacing8),
                  
                  // Refresh button
                  Container(
                    decoration: BoxDecoration(
                      color: AppTheme.surfaceVariantColor,
                      borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                    ),
                    child: IconButton(
                      onPressed: onRefresh,
                      icon: Icon(
                        Icons.refresh_outlined,
                        color: AppTheme.primaryColor,
                      ),
                      tooltip: 'Refresh',
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: AppTheme.spacing16),
          
          // Statistics
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  icon: Icons.picture_as_pdf_outlined,
                  label: 'PDFs',
                  count: totalPdfs,
                  color: AppTheme.errorColor,
                ),
              ),
              
              const SizedBox(width: AppTheme.spacing12),
              
              Expanded(
                child: _buildStatCard(
                  context,
                  icon: Icons.image_outlined,
                  label: 'Images',
                  count: totalImages,
                  color: AppTheme.secondaryColor,
                ),
              ),
              
              const SizedBox(width: AppTheme.spacing12),
              
              Expanded(
                child: _buildStatCard(
                  context,
                  icon: Icons.folder_outlined,
                  label: 'Total',
                  count: totalPdfs + totalImages,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required IconData icon,
    required String label,
    required int count,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSM),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: AppConstants.iconSizeMD,
          ),
          const SizedBox(height: AppTheme.spacing8),
          Text(
            count.toString(),
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textSecondaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

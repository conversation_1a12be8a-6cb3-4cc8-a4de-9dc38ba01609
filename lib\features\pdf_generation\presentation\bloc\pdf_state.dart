import 'package:equatable/equatable.dart';

abstract class PdfState extends Equatable {
  const PdfState();

  @override
  List<Object?> get props => [];
}

class PdfInitial extends PdfState {}

class PdfGenerating extends PdfState {}

class PdfGenerated extends PdfState {
  final String filePath;
  final String fileName;

  const PdfGenerated({
    required this.filePath,
    required this.fileName,
  });

  @override
  List<Object?> get props => [filePath, fileName];
}

class PdfSharing extends PdfState {}

class PdfShared extends PdfState {}

class PdfError extends PdfState {
  final String message;

  const PdfError({required this.message});

  @override
  List<Object?> get props => [message];
}

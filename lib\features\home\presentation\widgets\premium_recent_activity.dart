import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumRecentActivity extends StatefulWidget {
  const PremiumRecentActivity({super.key});

  @override
  State<PremiumRecentActivity> createState() => _PremiumRecentActivityState();
}

class _PremiumRecentActivityState extends State<PremiumRecentActivity>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<Offset>> _slideAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startStaggeredAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _slideAnimations = List.generate(5, (index) {
      return Tween<Offset>(
        begin: const Offset(0, 0.5),
        end: Offset.zero,
      ).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Interval(
            index * 0.1,
            0.5 + (index * 0.1),
            curve: AppTheme.curveEmphasized,
          ),
        ),
      );
    });
  }

  void _startStaggeredAnimations() {
    Future.delayed(const Duration(milliseconds: 600), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacing20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Activity',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              TextButton(
                onPressed: () {
                  HapticFeedback.lightImpact();
                  Navigator.of(context).pushNamed('/file-manager');
                },
                child: Text(
                  'View All',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.brandPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacing16),

          // Recent Documents List
          ..._getRecentDocuments().asMap().entries.map((entry) {
            final index = entry.key;
            final document = entry.value;

            return AnimatedBuilder(
              animation: _slideAnimations[index],
              builder: (context, child) {
                return SlideTransition(
                  position: _slideAnimations[index],
                  child: _buildDocumentCard(document, index),
                );
              },
            );
          }),

          // Empty State or Load More
          if (_getRecentDocuments().isEmpty)
            _buildEmptyState()
          else
            _buildLoadMoreButton(),
        ],
      ),
    );
  }

  Widget _buildDocumentCard(DocumentItem document, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacing12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusXL),
        border: Border.all(color: AppTheme.outlineColor, width: 1),
        boxShadow: [
          BoxShadow(
            color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
            blurRadius: AppTheme.shadowBlurMD,
            offset: const Offset(0, AppTheme.spacing4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppTheme.radiusXL),
          onTap: () {
            HapticFeedback.lightImpact();
            // Handle document tap
          },
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacing16),
            child: Row(
              children: [
                // Document Icon
                Container(
                  width: AppTheme.spacing48,
                  height: AppTheme.spacing48,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: document.gradient,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(AppTheme.radiusMD),
                    boxShadow: [
                      BoxShadow(
                        color: document.gradient.first.withValues(alpha: 0.3),
                        blurRadius: AppTheme.shadowBlurSM,
                        offset: const Offset(0, AppTheme.spacing4),
                      ),
                    ],
                  ),
                  child: Icon(
                    document.icon,
                    color: AppTheme.textOnPrimaryColor,
                    size: AppTheme.spacing24,
                  ),
                ),

                const SizedBox(width: AppTheme.spacing16),

                // Document Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        document.name,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimaryColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: AppTheme.spacing4),
                      Row(
                        children: [
                          Text(
                            document.size,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: AppTheme.textSecondaryColor),
                          ),
                          Text(
                            ' • ',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: AppTheme.textTertiaryColor),
                          ),
                          Text(
                            document.date,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: AppTheme.textTertiaryColor),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Action Button
                Container(
                  width: AppTheme.spacing32,
                  height: AppTheme.spacing32,
                  decoration: BoxDecoration(
                    color: AppTheme.backgroundSecondary,
                    borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                  ),
                  child: Icon(
                    Icons.more_vert_rounded,
                    color: AppTheme.textSecondaryColor,
                    size: AppTheme.spacing16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing32),
      child: Column(
        children: [
          Container(
            width: AppTheme.spacing64,
            height: AppTheme.spacing64,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: AppTheme.primaryGradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(AppTheme.radius2XL),
            ),
            child: Icon(
              Icons.description_outlined,
              color: AppTheme.textOnPrimaryColor,
              size: AppTheme.spacing32,
            ),
          ),

          const SizedBox(height: AppTheme.spacing16),

          Text(
            'No Recent Activity',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),

          const SizedBox(height: AppTheme.spacing8),

          Text(
            'Start scanning documents to see them here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadMoreButton() {
    return Container(
      margin: const EdgeInsets.only(top: AppTheme.spacing16),
      width: double.infinity,
      child: TextButton(
        onPressed: () {
          HapticFeedback.lightImpact();
          Navigator.of(context).pushNamed('/file-manager');
        },
        child: Text(
          'View All Documents',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppTheme.brandPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  List<DocumentItem> _getRecentDocuments() {
    // Mock data - replace with actual recent documents
    return [
      DocumentItem(
        name: 'Invoice_2024_001.pdf',
        size: '2.4 MB',
        date: '2 hours ago',
        icon: Icons.picture_as_pdf_outlined,
        gradient: [AppTheme.errorColor, AppTheme.errorLightColor],
      ),
      DocumentItem(
        name: 'Contract_Draft.pdf',
        size: '1.8 MB',
        date: 'Yesterday',
        icon: Icons.picture_as_pdf_outlined,
        gradient: [AppTheme.errorColor, AppTheme.errorLightColor],
      ),
      DocumentItem(
        name: 'Receipt_Grocery.jpg',
        size: '856 KB',
        date: '3 days ago',
        icon: Icons.image_outlined,
        gradient: [AppTheme.secondaryColor, AppTheme.secondaryLightColor],
      ),
      DocumentItem(
        name: 'Business_Card.png',
        size: '1.2 MB',
        date: '1 week ago',
        icon: Icons.image_outlined,
        gradient: [AppTheme.secondaryColor, AppTheme.secondaryLightColor],
      ),
    ];
  }
}

class DocumentItem {
  final String name;
  final String size;
  final String date;
  final IconData icon;
  final List<Color> gradient;

  DocumentItem({
    required this.name,
    required this.size,
    required this.date,
    required this.icon,
    required this.gradient,
  });
}

import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../widgets/premium_app_bar.dart';
import '../widgets/premium_stats_cards.dart';
import '../widgets/premium_scan_button.dart';
import '../widgets/premium_quick_actions.dart';
import '../widgets/premium_recent_documents.dart';
import '../widgets/premium_floating_scan_fab.dart';

class PremiumHomeScreen extends StatefulWidget {
  const PremiumHomeScreen({super.key});

  @override
  State<PremiumHomeScreen> createState() => _PremiumHomeScreenState();
}

class _PremiumHomeScreenState extends State<PremiumHomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _triggerEntryAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: AppTheme.animationSlow,
      vsync: this,
    );

    _slideController = AnimationController(
      duration: AppTheme.animationSlower,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: AppTheme.curveEmphasized),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _slideController,
        curve: AppTheme.curveEmphasized,
      ),
    );
  }

  void _triggerEntryAnimations() {
    Future.delayed(const Duration(milliseconds: 100), () {
      _fadeController.forward();
    });

    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: AnimatedBuilder(
          animation: Listenable.merge([_fadeAnimation, _slideAnimation]),
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: CustomScrollView(
                  physics: const BouncingScrollPhysics(),
                  slivers: [
                    // Premium App Bar
                    const PremiumAppBar(),

                    // Main Content
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacing20,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: AppTheme.spacing24),

                            // Welcome Section
                            _buildWelcomeSection(),

                            const SizedBox(height: AppTheme.spacing32),

                            // Stats Cards
                            const PremiumStatsCards(),

                            const SizedBox(height: AppTheme.spacing32),

                            // Main Scan Button
                            const PremiumScanButton(),

                            const SizedBox(height: AppTheme.spacing32),

                            // Quick Actions
                            const PremiumQuickActions(),

                            const SizedBox(height: AppTheme.spacing32),

                            // Recent Documents
                            const PremiumRecentDocuments(),

                            const SizedBox(height: AppTheme.spacing96),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
      floatingActionButton: const PremiumFloatingScanFAB(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildWelcomeSection() {
    final hour = DateTime.now().hour;
    String greeting;
    IconData greetingIcon;

    if (hour < 12) {
      greeting = 'Good Morning';
      greetingIcon = Icons.wb_sunny_outlined;
    } else if (hour < 17) {
      greeting = 'Good Afternoon';
      greetingIcon = Icons.wb_sunny_outlined;
    } else {
      greeting = 'Good Evening';
      greetingIcon = Icons.nights_stay_outlined;
    }

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: AppTheme.primaryGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radius2XL),
        boxShadow: [
          BoxShadow(
            color: AppTheme.brandPrimary.withValues(alpha: 0.3),
            blurRadius: AppTheme.shadowBlurLG,
            offset: const Offset(0, AppTheme.spacing8),
          ),
          BoxShadow(
            color: AppTheme.brandPrimary.withValues(alpha: 0.1),
            blurRadius: AppTheme.shadowBlur2XL,
            offset: const Offset(0, AppTheme.spacing16),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacing12),
            decoration: BoxDecoration(
              color: AppTheme.glassColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusLG),
              border: Border.all(color: AppTheme.glassBorderColor, width: 1),
            ),
            child: Icon(
              greetingIcon,
              color: AppTheme.textOnPrimaryColor,
              size: AppTheme.spacing28,
            ),
          ),

          const SizedBox(width: AppTheme.spacing16),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  greeting,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppTheme.textOnPrimaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppTheme.spacing4),
                Text(
                  'Ready to scan some documents?',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),

          Container(
            padding: const EdgeInsets.all(AppTheme.spacing8),
            decoration: BoxDecoration(
              color: AppTheme.glassColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusSM),
              border: Border.all(color: AppTheme.glassBorderColor, width: 1),
            ),
            child: Icon(
              Icons.chevron_right_rounded,
              color: AppTheme.textOnPrimaryColor,
              size: AppTheme.spacing20,
            ),
          ),
        ],
      ),
    );
  }
}

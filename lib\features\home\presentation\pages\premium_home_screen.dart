import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';
import '../widgets/premium_home_app_bar.dart';
import '../widgets/premium_quick_actions_row.dart';
import '../widgets/premium_recent_activity.dart';
import '../widgets/premium_bottom_navigation.dart';
import '../../../settings/presentation/pages/premium_settings_screen.dart';

class PremiumHomeScreen extends StatefulWidget {
  const PremiumHomeScreen({super.key});

  @override
  State<PremiumHomeScreen> createState() => _PremiumHomeScreenState();
}

class _PremiumHomeScreenState extends State<PremiumHomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late PageController _pageController;

  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _initializeAnimations();
    _startEntryAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _slideController = AnimationController(
      duration: AppTheme.animationSlow,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: AppTheme.curveEmphasized),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _slideController,
        curve: AppTheme.curveEmphasized,
      ),
    );
  }

  void _startEntryAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _slideController.forward();
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _onBottomNavTap(int index) {
    HapticFeedback.lightImpact();

    // Handle navigation based on new 3-tab structure
    switch (index) {
      case 0:
        // Home tab
        setState(() {
          _currentIndex = 0;
        });
        _pageController.animateToPage(
          0,
          duration: AppTheme.animationNormal,
          curve: AppTheme.curveEmphasized,
        );
        break;
      case 1:
        // Center scan button - navigate to camera
        Navigator.of(context).pushNamed('/camera');
        break;
      case 2:
        // Settings tab
        setState(() {
          _currentIndex = 2;
        });
        _pageController.animateToPage(
          1, // Second page in PageView (0: Home, 1: Settings)
          duration: AppTheme.animationNormal,
          curve: AppTheme.curveEmphasized,
        );
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: AnimatedBuilder(
        animation: Listenable.merge([_fadeAnimation, _slideAnimation]),
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: SafeArea(
                child: Column(
                  children: [
                    // App Bar/Header Section
                    const PremiumHomeAppBar(),

                    // Main Content - PageView
                    Expanded(
                      child: PageView(
                        controller: _pageController,
                        onPageChanged: (index) {
                          setState(() {
                            _currentIndex =
                                index == 0 ? 0 : 2; // Map to bottom nav indices
                          });
                        },
                        children: [
                          // Home Page
                          _buildHomePage(),

                          // Settings Page
                          const PremiumSettingsScreen(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
      bottomNavigationBar: PremiumBottomNavigation(
        currentIndex: _currentIndex,
        onTap: _onBottomNavTap,
      ),
    );
  }

  Widget _buildHomePage() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppTheme.spacing24),

          // Quick Actions Section
          const PremiumQuickActionsRow(),

          const SizedBox(height: AppTheme.spacing32),

          // Recent Activity/Documents Section
          const PremiumRecentActivity(),

          const SizedBox(height: AppTheme.spacing32),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_theme.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacing16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              const SizedBox(height: AppTheme.spacing32),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'LensDoc',
                          style: Theme.of(
                            context,
                          ).textTheme.displayMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacing8),
                        Text(
                          'Professional document scanning made simple',
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(color: AppTheme.textSecondaryColor),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      Navigator.of(context).pushNamed('/settings');
                    },
                    icon: Icon(
                      Icons.settings_outlined,
                      color: AppTheme.textSecondaryColor,
                      size: AppConstants.iconSizeLG,
                    ),
                    tooltip: 'Settings',
                  ),
                ],
              ),

              const SizedBox(height: AppTheme.spacing48),

              // Enhanced main scan button
              Center(
                child: Column(
                  children: [
                    _buildEnhancedScanButton(context),
                    const SizedBox(height: AppTheme.spacing24),
                    Text(
                      'Tap to start scanning documents',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),

              const Spacer(),

              // Quick actions
              Row(
                children: [
                  Expanded(
                    child: _buildQuickActionCard(
                      context,
                      icon: Icons.photo_library_outlined,
                      title: 'Gallery',
                      subtitle: 'Import from photos',
                      color: AppTheme.secondaryColor,
                      onTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: const Text('Gallery import coming soon!'),
                            backgroundColor: AppTheme.warningColor,
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                AppTheme.radiusSM,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  const SizedBox(width: AppTheme.spacing16),

                  Expanded(
                    child: _buildQuickActionCard(
                      context,
                      icon: Icons.folder_outlined,
                      title: 'Files',
                      subtitle: 'View saved docs',
                      color: AppTheme.accentColor,
                      onTap: () {
                        Navigator.of(context).pushNamed('/file-manager');
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppTheme.spacing32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedScanButton(BuildContext context) {
    return TweenAnimationBuilder<double>(
      duration: AppTheme.animationSlow,
      tween: Tween(begin: 0.0, end: 1.0),
      curve: AppTheme.curveEmphasized,
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * value),
          child: Opacity(
            opacity: value,
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: [AppTheme.primaryColor, AppTheme.primaryLightColor],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.3),
                    blurRadius: AppTheme.spacing32,
                    offset: const Offset(0, AppTheme.spacing12),
                  ),
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    blurRadius: AppTheme.spacing64,
                    offset: const Offset(0, AppTheme.spacing24),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                  onTap: () => _startScanning(context),
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppTheme.textOnPrimaryColor.withValues(
                          alpha: 0.2,
                        ),
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(AppTheme.spacing8),
                            decoration: BoxDecoration(
                              color: AppTheme.textOnPrimaryColor.withValues(
                                alpha: 0.1,
                              ),
                              borderRadius: BorderRadius.circular(
                                AppTheme.radiusLG,
                              ),
                            ),
                            child: Icon(
                              Icons.document_scanner_outlined,
                              size: AppTheme.spacing56,
                              color: AppTheme.textOnPrimaryColor,
                            ),
                          ),
                          const SizedBox(height: AppTheme.spacing12),
                          Text(
                            'Scan',
                            style: Theme.of(
                              context,
                            ).textTheme.titleLarge?.copyWith(
                              color: AppTheme.textOnPrimaryColor,
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return AnimatedContainer(
      duration: AppTheme.animationNormal,
      curve: AppTheme.curveStandard,
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusXL),
        border: Border.all(color: AppTheme.outlineColor, width: 1),
        boxShadow: [
          BoxShadow(
            color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
            blurRadius: AppTheme.spacing12,
            offset: const Offset(0, AppTheme.spacing6),
          ),
          BoxShadow(
            color: AppTheme.textPrimaryColor.withValues(alpha: 0.02),
            blurRadius: AppTheme.spacing24,
            offset: const Offset(0, AppTheme.spacing12),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppTheme.radiusXL),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacing24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: AppTheme.touchTargetComfortable,
                  height: AppTheme.touchTargetComfortable,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        color.withValues(alpha: 0.15),
                        color.withValues(alpha: 0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(AppTheme.radiusMD),
                  ),
                  child: Icon(
                    icon,
                    size: AppConstants.iconSizeLG,
                    color: color,
                  ),
                ),
                const SizedBox(height: AppTheme.spacing20),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: AppTheme.spacing6),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _startScanning(BuildContext context) async {
    // Check camera permission
    final cameraStatus = await Permission.camera.status;

    if (cameraStatus.isDenied) {
      final result = await Permission.camera.request();
      if (result.isDenied) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Camera permission is required to scan documents'),
            ),
          );
        }
        return;
      }
    }

    if (cameraStatus.isPermanentlyDenied) {
      if (context.mounted) {
        _showPermissionDialog(context);
      }
      return;
    }

    // Navigate to camera screen
    if (context.mounted) {
      Navigator.of(context).pushNamed('/camera');
    }
  }

  void _showPermissionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Camera Permission Required'),
            content: const Text(
              'This app needs camera access to scan documents. Please enable camera permission in your device settings.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  openAppSettings();
                },
                child: const Text('Settings'),
              ),
            ],
          ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Premium Professional Color Palette - Sophisticated Blue & Purple Gradient System

  // Primary Colors - Deep blue to purple gradient for premium feel
  static const Color primaryColor = Color(0xFF0F172A); // Deep navy
  static const Color primaryLightColor = Color(0xFF1E293B); // Slate
  static const Color primaryDarkColor = Color(0xFF020617); // Almost black

  // Brand Colors - Sophisticated blue gradient
  static const Color brandPrimary = Color(0xFF3B82F6); // Bright blue
  static const Color brandSecondary = Color(0xFF8B5CF6); // Purple
  static const Color brandTertiary = Color(0xFF06B6D4); // Cyan

  // Gradient Colors for premium effects
  static const List<Color> primaryGradient = [
    Color(0xFF3B82F6), // Blue
    Color(0xFF8B5CF6), // Purple
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFF06B6D4), // Cyan
    Color(0xFF3B82F6), // Blue
  ];

  static const List<Color> accentGradient = [
    Color(0xFFFF6B35), // Orange
    Color(0xFFFF8A65), // Light orange
  ];

  // Secondary Colors - Sophisticated teal and cyan
  static const Color secondaryColor = Color(0xFF06B6D4); // Cyan
  static const Color secondaryLightColor = Color(0xFF22D3EE); // Light cyan
  static const Color secondaryDarkColor = Color(0xFF0891B2); // Dark cyan

  // Accent Colors - Premium orange and coral
  static const Color accentColor = Color(0xFFFF6B35); // Coral
  static const Color accentLightColor = Color(0xFFFF8A65); // Light coral
  static const Color accentDarkColor = Color(0xFFEA580C); // Dark orange

  // Premium Neutral Colors - Sophisticated grayscale system
  static const Color backgroundColor = Color(0xFFF8FAFC); // Premium light
  static const Color backgroundSecondary = Color(
    0xFFF1F5F9,
  ); // Secondary background
  static const Color surfaceColor = Color(0xFFFFFFFF); // Pure white
  static const Color surfaceVariantColor = Color(0xFFF8FAFC); // Light variant
  static const Color surfaceElevatedColor = Color(
    0xFFFFFFFF,
  ); // Elevated surface
  static const Color surfaceDimColor = Color(0xFFF1F5F9); // Dimmed surface
  static const Color outlineColor = Color(0xFFE2E8F0); // Border
  static const Color outlineVariantColor = Color(0xFFCBD5E1); // Variant border
  static const Color dividerColor = Color(0xFFE2E8F0); // Divider

  // Premium Glass Effect Colors
  static const Color glassColor = Color(0x1AFFFFFF); // Glass overlay
  static const Color glassBorderColor = Color(0x33FFFFFF); // Glass border

  // Text Colors - Premium hierarchy
  static const Color textPrimaryColor = Color(0xFF0F172A); // Primary text
  static const Color textSecondaryColor = Color(0xFF475569); // Secondary text
  static const Color textTertiaryColor = Color(0xFF94A3B8); // Tertiary text
  static const Color textOnPrimaryColor = Color(0xFFFFFFFF); // On primary
  static const Color textOnSurfaceColor = Color(0xFF0F172A); // On surface
  static const Color textDisabledColor = Color(0xFFCBD5E1); // Disabled text

  // Premium Semantic Colors
  static const Color successColor = Color(0xFF10B981); // Emerald
  static const Color successLightColor = Color(0xFF34D399); // Light emerald
  static const Color successDarkColor = Color(0xFF059669); // Dark emerald
  static const Color warningColor = Color(0xFFF59E0B); // Amber
  static const Color warningLightColor = Color(0xFFFBBF24); // Light amber
  static const Color warningDarkColor = Color(0xFFD97706); // Dark amber
  static const Color errorColor = Color(0xFFEF4444); // Red
  static const Color errorLightColor = Color(0xFFF87171); // Light red
  static const Color errorDarkColor = Color(0xFFDC2626); // Dark red
  static const Color infoColor = Color(0xFF3B82F6); // Blue
  static const Color infoLightColor = Color(0xFF60A5FA); // Light blue
  static const Color infoDarkColor = Color(0xFF2563EB); // Dark blue

  // Dark Theme Colors
  static const Color darkBackgroundColor = Color(0xFF0F172A); // Dark slate
  static const Color darkSurfaceColor = Color(0xFF1E293B); // Dark gray
  static const Color darkSurfaceVariantColor = Color(0xFF334155); // Medium dark
  static const Color darkTextPrimaryColor = Color(0xFFF8FAFC); // Light text
  static const Color darkTextSecondaryColor = Color(
    0xFFCBD5E1,
  ); // Medium light text

  // Premium Design Tokens - Advanced Spacing System (4dp grid + golden ratio)
  static const double spacing1 = 1.0;
  static const double spacing2 = 2.0;
  static const double spacing4 = 4.0;
  static const double spacing6 = 6.0;
  static const double spacing8 = 8.0;
  static const double spacing10 = 10.0;
  static const double spacing12 = 12.0;
  static const double spacing14 = 14.0;
  static const double spacing16 = 16.0;
  static const double spacing18 = 18.0;
  static const double spacing20 = 20.0;
  static const double spacing24 = 24.0;
  static const double spacing28 = 28.0;
  static const double spacing32 = 32.0;
  static const double spacing36 = 36.0;
  static const double spacing40 = 40.0;
  static const double spacing44 = 44.0;
  static const double spacing48 = 48.0;
  static const double spacing52 = 52.0;
  static const double spacing56 = 56.0;
  static const double spacing64 = 64.0;
  static const double spacing72 = 72.0;
  static const double spacing80 = 80.0;
  static const double spacing96 = 96.0;
  static const double spacing112 = 112.0;
  static const double spacing128 = 128.0;
  static const double spacing144 = 144.0;
  static const double spacing160 = 160.0;

  // Premium Border Radius System - Sophisticated curves
  static const double radiusXS = 4.0; // Micro elements
  static const double radiusSM = 8.0; // Small buttons, chips
  static const double radiusMD = 12.0; // Standard cards, inputs
  static const double radiusLG = 16.0; // Large cards
  static const double radiusXL = 20.0; // Extra large components
  static const double radius2XL = 24.0; // Hero cards
  static const double radius3XL = 32.0; // Premium hero elements
  static const double radius4XL = 40.0; // Ultra premium elements
  static const double radiusRound = 999.0; // Fully rounded

  // Premium Glass Effect Radius
  static const double radiusGlass = 20.0; // Glass morphism elements

  // Advanced Elevation System - Premium depth hierarchy
  static const double elevation0 = 0.0; // Flat elements
  static const double elevation1 = 1.0; // Subtle depth
  static const double elevation2 = 2.0; // Standard cards
  static const double elevation3 = 3.0; // Raised cards
  static const double elevation4 = 4.0; // Interactive elements
  static const double elevation6 = 6.0; // Floating elements
  static const double elevation8 = 8.0; // Modals, sheets
  static const double elevation12 = 12.0; // Navigation elements
  static const double elevation16 = 16.0; // App bars
  static const double elevation20 = 20.0; // Premium floating
  static const double elevation24 = 24.0; // Dialogs
  static const double elevation32 = 32.0; // Premium overlays

  // Premium Shadow Blur Radius
  static const double shadowBlurSM = 4.0;
  static const double shadowBlurMD = 8.0;
  static const double shadowBlurLG = 16.0;
  static const double shadowBlurXL = 24.0;
  static const double shadowBlur2XL = 32.0;
  static const double shadowBlur3XL = 48.0;

  // Typography Scale
  static const double fontSizeXS = 12.0;
  static const double fontSizeSM = 14.0;
  static const double fontSizeBase = 16.0;
  static const double fontSizeLG = 18.0;
  static const double fontSizeXL = 20.0;
  static const double fontSize2XL = 24.0;
  static const double fontSize3XL = 30.0;
  static const double fontSize4XL = 36.0;

  // Line Heights
  static const double lineHeightTight = 1.25;
  static const double lineHeightNormal = 1.5;
  static const double lineHeightRelaxed = 1.75;

  // Animation & Transition Constants
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationNormal = Duration(milliseconds: 250);
  static const Duration animationSlow = Duration(milliseconds: 350);
  static const Duration animationSlower = Duration(milliseconds: 500);

  // Animation Curves
  static const Curve curveStandard = Curves.easeInOut;
  static const Curve curveDecelerate = Curves.easeOut;
  static const Curve curveAccelerate = Curves.easeIn;
  static const Curve curveEmphasized = Curves.easeInOutCubic;

  // Touch Target Sizes (Accessibility)
  static const double touchTargetMin = 44.0; // Minimum touch target
  static const double touchTargetComfortable = 48.0; // Comfortable touch target
  static const double touchTargetLarge = 56.0; // Large touch target

  // Light Theme
  static ThemeData get lightTheme {
    final textTheme = GoogleFonts.interTextTheme().copyWith(
      // Display styles
      displayLarge: GoogleFonts.inter(
        fontSize: fontSize4XL,
        fontWeight: FontWeight.w700,
        height: lineHeightTight,
        color: textPrimaryColor,
      ),
      displayMedium: GoogleFonts.inter(
        fontSize: fontSize3XL,
        fontWeight: FontWeight.w600,
        height: lineHeightTight,
        color: textPrimaryColor,
      ),
      // Headline styles
      headlineLarge: GoogleFonts.inter(
        fontSize: fontSize2XL,
        fontWeight: FontWeight.w600,
        height: lineHeightTight,
        color: textPrimaryColor,
      ),
      headlineMedium: GoogleFonts.inter(
        fontSize: fontSizeXL,
        fontWeight: FontWeight.w600,
        height: lineHeightNormal,
        color: textPrimaryColor,
      ),
      // Title styles
      titleLarge: GoogleFonts.inter(
        fontSize: fontSizeLG,
        fontWeight: FontWeight.w500,
        height: lineHeightNormal,
        color: textPrimaryColor,
      ),
      titleMedium: GoogleFonts.inter(
        fontSize: fontSizeBase,
        fontWeight: FontWeight.w500,
        height: lineHeightNormal,
        color: textPrimaryColor,
      ),
      // Body styles
      bodyLarge: GoogleFonts.inter(
        fontSize: fontSizeBase,
        fontWeight: FontWeight.w400,
        height: lineHeightRelaxed,
        color: textPrimaryColor,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: fontSizeSM,
        fontWeight: FontWeight.w400,
        height: lineHeightNormal,
        color: textSecondaryColor,
      ),
      bodySmall: GoogleFonts.inter(
        fontSize: fontSizeXS,
        fontWeight: FontWeight.w400,
        height: lineHeightNormal,
        color: textTertiaryColor,
      ),
      // Label styles
      labelLarge: GoogleFonts.inter(
        fontSize: fontSizeSM,
        fontWeight: FontWeight.w500,
        height: lineHeightNormal,
        color: textPrimaryColor,
      ),
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.light(
        primary: primaryColor,
        onPrimary: textOnPrimaryColor,
        primaryContainer: primaryLightColor,
        onPrimaryContainer: textOnPrimaryColor,
        secondary: secondaryColor,
        onSecondary: textOnPrimaryColor,
        tertiary: accentColor,
        onTertiary: textOnPrimaryColor,
        error: errorColor,
        onError: textOnPrimaryColor,
        surface: surfaceColor,
        onSurface: textPrimaryColor,
        surfaceContainerHighest: surfaceVariantColor,
        onSurfaceVariant: textSecondaryColor,
        outline: outlineColor,
      ),
      textTheme: textTheme,
      scaffoldBackgroundColor: backgroundColor,
      appBarTheme: AppBarTheme(
        elevation: elevation0,
        centerTitle: true,
        backgroundColor: surfaceColor,
        foregroundColor: textPrimaryColor,
        surfaceTintColor: Colors.transparent,
        titleTextStyle: textTheme.titleLarge,
        toolbarHeight: 64,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: elevation2,
          padding: const EdgeInsets.symmetric(
            horizontal: spacing24,
            vertical: spacing12,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusSM),
          ),
          textStyle: textTheme.labelLarge,
        ),
      ),
      cardTheme: CardTheme(
        elevation: elevation2,
        color: surfaceColor,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMD),
        ),
        margin: const EdgeInsets.all(spacing8),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusSM),
          borderSide: const BorderSide(color: outlineColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusSM),
          borderSide: const BorderSide(color: outlineColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusSM),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        filled: true,
        fillColor: surfaceColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacing16,
          vertical: spacing12,
        ),
      ),
    );
  }

  // Dark Theme
  static ThemeData get darkTheme {
    final darkTextTheme = GoogleFonts.interTextTheme(
      ThemeData.dark().textTheme,
    ).copyWith(
      // Display styles
      displayLarge: GoogleFonts.inter(
        fontSize: fontSize4XL,
        fontWeight: FontWeight.w700,
        height: lineHeightTight,
        color: darkTextPrimaryColor,
      ),
      displayMedium: GoogleFonts.inter(
        fontSize: fontSize3XL,
        fontWeight: FontWeight.w600,
        height: lineHeightTight,
        color: darkTextPrimaryColor,
      ),
      // Headline styles
      headlineLarge: GoogleFonts.inter(
        fontSize: fontSize2XL,
        fontWeight: FontWeight.w600,
        height: lineHeightTight,
        color: darkTextPrimaryColor,
      ),
      headlineMedium: GoogleFonts.inter(
        fontSize: fontSizeXL,
        fontWeight: FontWeight.w600,
        height: lineHeightNormal,
        color: darkTextPrimaryColor,
      ),
      // Title styles
      titleLarge: GoogleFonts.inter(
        fontSize: fontSizeLG,
        fontWeight: FontWeight.w500,
        height: lineHeightNormal,
        color: darkTextPrimaryColor,
      ),
      titleMedium: GoogleFonts.inter(
        fontSize: fontSizeBase,
        fontWeight: FontWeight.w500,
        height: lineHeightNormal,
        color: darkTextPrimaryColor,
      ),
      // Body styles
      bodyLarge: GoogleFonts.inter(
        fontSize: fontSizeBase,
        fontWeight: FontWeight.w400,
        height: lineHeightRelaxed,
        color: darkTextPrimaryColor,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: fontSizeSM,
        fontWeight: FontWeight.w400,
        height: lineHeightNormal,
        color: darkTextSecondaryColor,
      ),
      bodySmall: GoogleFonts.inter(
        fontSize: fontSizeXS,
        fontWeight: FontWeight.w400,
        height: lineHeightNormal,
        color: darkTextSecondaryColor,
      ),
      // Label styles
      labelLarge: GoogleFonts.inter(
        fontSize: fontSizeSM,
        fontWeight: FontWeight.w500,
        height: lineHeightNormal,
        color: darkTextPrimaryColor,
      ),
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.dark(
        primary: primaryLightColor,
        onPrimary: textPrimaryColor,
        primaryContainer: primaryColor,
        onPrimaryContainer: darkTextPrimaryColor,
        secondary: secondaryLightColor,
        onSecondary: textPrimaryColor,
        tertiary: accentLightColor,
        onTertiary: textPrimaryColor,
        error: errorLightColor,
        onError: textPrimaryColor,
        surface: darkSurfaceColor,
        onSurface: darkTextPrimaryColor,
        surfaceContainerHighest: darkSurfaceVariantColor,
        onSurfaceVariant: darkTextSecondaryColor,
        outline: outlineVariantColor,
      ),
      textTheme: darkTextTheme,
      scaffoldBackgroundColor: darkBackgroundColor,
      appBarTheme: AppBarTheme(
        elevation: elevation0,
        centerTitle: true,
        backgroundColor: darkSurfaceColor,
        foregroundColor: darkTextPrimaryColor,
        surfaceTintColor: Colors.transparent,
        titleTextStyle: darkTextTheme.titleLarge,
        toolbarHeight: 64,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: elevation2,
          padding: const EdgeInsets.symmetric(
            horizontal: spacing24,
            vertical: spacing12,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusSM),
          ),
          textStyle: darkTextTheme.labelLarge,
        ),
      ),
      cardTheme: CardTheme(
        elevation: elevation2,
        color: darkSurfaceColor,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMD),
        ),
        margin: const EdgeInsets.all(spacing8),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusSM),
          borderSide: const BorderSide(color: outlineVariantColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusSM),
          borderSide: const BorderSide(color: outlineVariantColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusSM),
          borderSide: const BorderSide(color: primaryLightColor, width: 2),
        ),
        filled: true,
        fillColor: darkSurfaceColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacing16,
          vertical: spacing12,
        ),
      ),
    );
  }
}

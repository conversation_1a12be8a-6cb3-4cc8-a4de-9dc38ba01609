import 'package:flutter/material.dart';
import 'dart:io';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';
import '../widgets/document_grid_item.dart';
import '../widgets/file_manager_header.dart';
import '../widgets/search_bar_widget.dart';

class FileManagerScreen extends StatefulWidget {
  const FileManagerScreen({super.key});

  @override
  State<FileManagerScreen> createState() => _FileManagerScreenState();
}

class _FileManagerScreenState extends State<FileManagerScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<File> _pdfFiles = [];
  List<File> _imageFiles = [];
  List<File> _filteredPdfs = [];
  List<File> _filteredImages = [];
  String _searchQuery = '';
  bool _isLoading = true;
  bool _isGridView = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadFiles();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadFiles() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final pdfs = await FileUtils.getAllPdfs();
      final images = await FileUtils.getAllScannedImages();

      setState(() {
        _pdfFiles = pdfs;
        _imageFiles = images;
        _filteredPdfs = pdfs;
        _filteredImages = images;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading files: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _filterFiles(String query) {
    setState(() {
      _searchQuery = query;
      
      if (query.isEmpty) {
        _filteredPdfs = _pdfFiles;
        _filteredImages = _imageFiles;
      } else {
        _filteredPdfs = _pdfFiles.where((file) {
          final fileName = file.path.split('/').last.toLowerCase();
          return fileName.contains(query.toLowerCase());
        }).toList();
        
        _filteredImages = _imageFiles.where((file) {
          final fileName = file.path.split('/').last.toLowerCase();
          return fileName.contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            FileManagerHeader(
              totalPdfs: _pdfFiles.length,
              totalImages: _imageFiles.length,
              isGridView: _isGridView,
              onViewToggle: () {
                setState(() {
                  _isGridView = !_isGridView;
                });
              },
              onRefresh: _loadFiles,
            ),
            
            // Search bar
            SearchBarWidget(
              onSearchChanged: _filterFiles,
              hintText: 'Search documents...',
            ),
            
            // Tab bar
            Container(
              margin: const EdgeInsets.symmetric(horizontal: AppTheme.spacing16),
              decoration: BoxDecoration(
                color: AppTheme.surfaceVariantColor,
                borderRadius: BorderRadius.circular(AppTheme.radiusSM),
              ),
              child: TabBar(
                controller: _tabController,
                indicator: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                ),
                labelColor: AppTheme.textOnPrimaryColor,
                unselectedLabelColor: AppTheme.textSecondaryColor,
                labelStyle: TextStyle(
                  fontSize: AppTheme.fontSizeSM,
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: TextStyle(
                  fontSize: AppTheme.fontSizeSM,
                  fontWeight: FontWeight.w500,
                ),
                tabs: [
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.picture_as_pdf_outlined, size: 18),
                        const SizedBox(width: AppTheme.spacing8),
                        Text('PDFs (${_filteredPdfs.length})'),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.image_outlined, size: 18),
                        const SizedBox(width: AppTheme.spacing8),
                        Text('Images (${_filteredImages.length})'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: AppTheme.spacing16),
            
            // Content
            Expanded(
              child: _isLoading
                  ? _buildLoadingState()
                  : TabBarView(
                      controller: _tabController,
                      children: [
                        // PDFs tab
                        _buildFileList(
                          files: _filteredPdfs,
                          emptyMessage: _searchQuery.isEmpty
                              ? 'No PDF documents found'
                              : 'No PDFs match your search',
                          emptyIcon: Icons.picture_as_pdf_outlined,
                          fileType: 'PDF',
                        ),
                        
                        // Images tab
                        _buildFileList(
                          files: _filteredImages,
                          emptyMessage: _searchQuery.isEmpty
                              ? 'No scanned images found'
                              : 'No images match your search',
                          emptyIcon: Icons.image_outlined,
                          fileType: 'Image',
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.of(context).pushNamed('/camera');
        },
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: AppTheme.textOnPrimaryColor,
        icon: const Icon(Icons.add_a_photo_outlined),
        label: const Text('Scan'),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppTheme.primaryColor,
          ),
          const SizedBox(height: AppTheme.spacing16),
          Text(
            'Loading documents...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFileList({
    required List<File> files,
    required String emptyMessage,
    required IconData emptyIcon,
    required String fileType,
  }) {
    if (files.isEmpty) {
      return _buildEmptyState(emptyMessage, emptyIcon);
    }

    if (_isGridView) {
      return GridView.builder(
        padding: const EdgeInsets.all(AppTheme.spacing16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: AppTheme.spacing12,
          mainAxisSpacing: AppTheme.spacing12,
          childAspectRatio: 0.75,
        ),
        itemCount: files.length,
        itemBuilder: (context, index) {
          return DocumentGridItem(
            file: files[index],
            fileType: fileType,
            onTap: () => _openFile(files[index]),
            onDelete: () => _deleteFile(files[index]),
            onShare: () => _shareFile(files[index]),
          );
        },
      );
    } else {
      return ListView.builder(
        padding: const EdgeInsets.all(AppTheme.spacing16),
        itemCount: files.length,
        itemBuilder: (context, index) {
          return DocumentGridItem(
            file: files[index],
            fileType: fileType,
            isListView: true,
            onTap: () => _openFile(files[index]),
            onDelete: () => _deleteFile(files[index]),
            onShare: () => _shareFile(files[index]),
          );
        },
      );
    }
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: AppTheme.spacing64,
            color: AppTheme.textTertiaryColor,
          ),
          const SizedBox(height: AppTheme.spacing16),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacing8),
          Text(
            _searchQuery.isEmpty
                ? 'Start scanning to create your first document'
                : 'Try adjusting your search terms',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textTertiaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          if (_searchQuery.isEmpty) ...[
            const SizedBox(height: AppTheme.spacing24),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pushNamed('/camera');
              },
              icon: const Icon(Icons.add_a_photo_outlined),
              label: const Text('Start Scanning'),
            ),
          ],
        ],
      ),
    );
  }

  void _openFile(File file) {
    // TODO: Implement file opening
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${file.path.split('/').last}'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _deleteFile(File file) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete File'),
        content: Text(
          'Are you sure you want to delete "${file.path.split('/').last}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              
              try {
                await file.delete();
                await _loadFiles();
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('File deleted successfully'),
                      backgroundColor: AppTheme.successColor,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error deleting file: $e'),
                      backgroundColor: AppTheme.errorColor,
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.errorColor,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _shareFile(File file) {
    // TODO: Implement file sharing
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sharing ${file.path.split('/').last}'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }
}

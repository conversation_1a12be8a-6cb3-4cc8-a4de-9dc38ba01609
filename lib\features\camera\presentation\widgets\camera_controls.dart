import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';

class CameraControls extends StatelessWidget {
  final VoidCallback onCapture;
  final bool isCapturing;

  const CameraControls({
    super.key,
    required this.onCapture,
    this.isCapturing = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: [
            AppTheme.textPrimaryColor.withValues(alpha: 0.8),
            AppTheme.textPrimaryColor.withValues(alpha: 0.0),
          ],
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Gallery Button
          Container(
            decoration: BoxDecoration(
              color: AppTheme.textPrimaryColor.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(AppTheme.radiusRound),
            ),
            child: IconButton(
              onPressed:
                  isCapturing
                      ? null
                      : () {
                        // Gallery import placeholder
                      },
              icon: Icon(
                Icons.photo_library_outlined,
                color:
                    isCapturing
                        ? AppTheme.textTertiaryColor
                        : AppTheme.textOnPrimaryColor,
                size: AppConstants.iconSizeLG,
              ),
            ),
          ),

          // Capture Button
          GestureDetector(
            onTap: isCapturing ? null : onCapture,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppTheme.textOnPrimaryColor,
                  width: 4,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.textPrimaryColor.withValues(alpha: 0.3),
                    blurRadius: AppTheme.spacing8,
                    offset: const Offset(0, AppTheme.spacing4),
                  ),
                ],
              ),
              child: Container(
                margin: const EdgeInsets.all(AppTheme.spacing8),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color:
                      isCapturing
                          ? AppTheme.errorColor
                          : AppTheme.textOnPrimaryColor,
                ),
                child:
                    isCapturing
                        ? Center(
                          child: SizedBox(
                            width: AppTheme.spacing24,
                            height: AppTheme.spacing24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppTheme.textOnPrimaryColor,
                              ),
                            ),
                          ),
                        )
                        : Icon(
                          Icons.camera_alt,
                          color: AppTheme.textPrimaryColor,
                          size: AppTheme.spacing24,
                        ),
              ),
            ),
          ),

          // Settings Button
          Container(
            decoration: BoxDecoration(
              color: AppTheme.textPrimaryColor.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(AppTheme.radiusRound),
            ),
            child: IconButton(
              onPressed:
                  isCapturing
                      ? null
                      : () {
                        // Settings placeholder
                      },
              icon: Icon(
                Icons.settings_outlined,
                color:
                    isCapturing
                        ? AppTheme.textTertiaryColor
                        : AppTheme.textOnPrimaryColor,
                size: AppConstants.iconSizeLG,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

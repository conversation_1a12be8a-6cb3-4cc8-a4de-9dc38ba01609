import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';

class CameraControls extends StatefulWidget {
  final VoidCallback onCapture;
  final bool isCapturing;

  const CameraControls({
    super.key,
    required this.onCapture,
    this.isCapturing = false,
  });

  @override
  State<CameraControls> createState() => _CameraControlsState();
}

class _CameraControlsState extends State<CameraControls>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: AppTheme.animationSlower,
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: AppTheme.curveStandard),
    );

    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: [
            AppTheme.textPrimaryColor.withValues(alpha: 0.8),
            AppTheme.textPrimaryColor.withValues(alpha: 0.0),
          ],
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Gallery Button
          _buildControlButton(
            icon: Icons.photo_library_outlined,
            onPressed:
                widget.isCapturing
                    ? null
                    : () {
                      // Gallery import placeholder
                    },
          ),

          // Enhanced Capture Button
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: widget.isCapturing ? _pulseAnimation.value : 1.0,
                child: GestureDetector(
                  onTap: widget.isCapturing ? null : widget.onCapture,
                  child: Container(
                    width: 88,
                    height: 88,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppTheme.textOnPrimaryColor,
                        width: 4,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.textPrimaryColor.withValues(
                            alpha: 0.4,
                          ),
                          blurRadius: AppTheme.spacing16,
                          offset: const Offset(0, AppTheme.spacing8),
                        ),
                        if (widget.isCapturing)
                          BoxShadow(
                            color: AppTheme.accentColor.withValues(alpha: 0.3),
                            blurRadius: AppTheme.spacing24,
                            offset: const Offset(0, AppTheme.spacing12),
                          ),
                      ],
                    ),
                    child: Container(
                      margin: const EdgeInsets.all(AppTheme.spacing10),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color:
                            widget.isCapturing
                                ? AppTheme.accentColor
                                : AppTheme.textOnPrimaryColor,
                      ),
                      child:
                          widget.isCapturing
                              ? Center(
                                child: SizedBox(
                                  width: AppTheme.spacing28,
                                  height: AppTheme.spacing28,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 3,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppTheme.textOnPrimaryColor,
                                    ),
                                  ),
                                ),
                              )
                              : Icon(
                                Icons.camera_alt,
                                color: AppTheme.textPrimaryColor,
                                size: AppTheme.spacing32,
                              ),
                    ),
                  ),
                ),
              );
            },
          ),

          // Settings Button
          _buildControlButton(
            icon: Icons.tune_outlined,
            onPressed:
                widget.isCapturing
                    ? null
                    : () {
                      // Settings placeholder
                    },
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
  }) {
    return Container(
      width: AppTheme.touchTargetLarge,
      height: AppTheme.touchTargetLarge,
      decoration: BoxDecoration(
        color: AppTheme.textPrimaryColor.withValues(alpha: 0.25),
        borderRadius: BorderRadius.circular(AppTheme.radiusLG),
        border: Border.all(
          color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppTheme.radiusLG),
          onTap: onPressed,
          child: Icon(
            icon,
            color:
                onPressed != null
                    ? AppTheme.textOnPrimaryColor
                    : AppTheme.textTertiaryColor,
            size: AppTheme.spacing28,
          ),
        ),
      ),
    );
  }
}

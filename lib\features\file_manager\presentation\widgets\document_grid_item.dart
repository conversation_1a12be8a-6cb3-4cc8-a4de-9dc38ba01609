import 'package:flutter/material.dart';
import 'dart:io';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';

class DocumentGridItem extends StatelessWidget {
  final File file;
  final String fileType;
  final bool isListView;
  final VoidCallback onTap;
  final VoidCallback onDelete;
  final VoidCallback onShare;

  const DocumentGridItem({
    super.key,
    required this.file,
    required this.fileType,
    this.isListView = false,
    required this.onTap,
    required this.onDelete,
    required this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    final fileName = file.path.split('/').last;
    final fileSize = FileUtils.getFileSizeInMB(file);
    final modifiedDate = file.lastModifiedSync();

    if (isListView) {
      return _buildListItem(context, fileName, fileSize, modifiedDate);
    } else {
      return _buildGridItem(context, fileName, fileSize, modifiedDate);
    }
  }

  Widget _buildGridItem(BuildContext context, String fileName, double fileSize, DateTime modifiedDate) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusMD),
        border: Border.all(
          color: AppTheme.outlineColor,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.textPrimaryColor.withValues(alpha: 0.05),
            blurRadius: AppTheme.spacing8,
            offset: const Offset(0, AppTheme.spacing4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppTheme.radiusMD),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacing12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // File icon and actions
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(AppTheme.spacing8),
                      decoration: BoxDecoration(
                        color: _getFileColor().withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                      ),
                      child: Icon(
                        _getFileIcon(),
                        color: _getFileColor(),
                        size: AppConstants.iconSizeLG,
                      ),
                    ),
                    
                    const Spacer(),
                    
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        switch (value) {
                          case 'share':
                            onShare();
                            break;
                          case 'delete':
                            onDelete();
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'share',
                          child: Row(
                            children: [
                              Icon(
                                Icons.share_outlined,
                                size: AppConstants.iconSizeSM,
                                color: AppTheme.textSecondaryColor,
                              ),
                              const SizedBox(width: AppTheme.spacing8),
                              const Text('Share'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(
                                Icons.delete_outline,
                                size: AppConstants.iconSizeSM,
                                color: AppTheme.errorColor,
                              ),
                              const SizedBox(width: AppTheme.spacing8),
                              Text(
                                'Delete',
                                style: TextStyle(color: AppTheme.errorColor),
                              ),
                            ],
                          ),
                        ),
                      ],
                      child: Icon(
                        Icons.more_vert_outlined,
                        color: AppTheme.textSecondaryColor,
                        size: AppConstants.iconSizeMD,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: AppTheme.spacing12),
                
                // File name
                Expanded(
                  child: Text(
                    fileName,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                
                const SizedBox(height: AppTheme.spacing8),
                
                // File info
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${fileSize.toStringAsFixed(1)} MB',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacing2),
                    Text(
                      _formatDate(modifiedDate),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textTertiaryColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildListItem(BuildContext context, String fileName, double fileSize, DateTime modifiedDate) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacing8),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusSM),
        border: Border.all(
          color: AppTheme.outlineColor,
          width: 1,
        ),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(AppTheme.spacing8),
          decoration: BoxDecoration(
            color: _getFileColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusSM),
          ),
          child: Icon(
            _getFileIcon(),
            color: _getFileColor(),
            size: AppConstants.iconSizeMD,
          ),
        ),
        title: Text(
          fileName,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          '${fileSize.toStringAsFixed(1)} MB • ${_formatDate(modifiedDate)}',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppTheme.textSecondaryColor,
          ),
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'share':
                onShare();
                break;
              case 'delete':
                onDelete();
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(
                    Icons.share_outlined,
                    size: AppConstants.iconSizeSM,
                    color: AppTheme.textSecondaryColor,
                  ),
                  const SizedBox(width: AppTheme.spacing8),
                  const Text('Share'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(
                    Icons.delete_outline,
                    size: AppConstants.iconSizeSM,
                    color: AppTheme.errorColor,
                  ),
                  const SizedBox(width: AppTheme.spacing8),
                  Text(
                    'Delete',
                    style: TextStyle(color: AppTheme.errorColor),
                  ),
                ],
              ),
            ),
          ],
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.radiusSM),
        ),
      ),
    );
  }

  IconData _getFileIcon() {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf_outlined;
      case 'image':
        return Icons.image_outlined;
      default:
        return Icons.description_outlined;
    }
  }

  Color _getFileColor() {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return AppTheme.errorColor;
      case 'image':
        return AppTheme.secondaryColor;
      default:
        return AppTheme.primaryColor;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

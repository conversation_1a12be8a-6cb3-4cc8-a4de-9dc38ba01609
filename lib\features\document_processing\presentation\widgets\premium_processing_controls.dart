import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumProcessingControls extends StatelessWidget {
  final bool isProcessing;
  final VoidCallback onFiltersToggle;
  final VoidCallback onSave;
  final VoidCallback onCancel;
  final bool showFilters;

  const PremiumProcessingControls({
    super.key,
    required this.isProcessing,
    required this.onFiltersToggle,
    required this.onSave,
    required this.onCancel,
    required this.showFilters,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        padding: EdgeInsets.only(
          left: AppTheme.spacing24,
          right: AppTheme.spacing24,
          bottom: MediaQuery.of(context).padding.bottom + AppTheme.spacing24,
          top: AppTheme.spacing24,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.transparent,
              AppTheme.primaryDarkColor.withValues(alpha: 0.8),
              AppTheme.primaryDarkColor,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Cancel Button
            _buildControlButton(
              icon: Icons.close_rounded,
              label: 'Cancel',
              onPressed: isProcessing ? null : onCancel,
              isSecondary: true,
            ),
            
            // Filters Button
            _buildControlButton(
              icon: Icons.tune_rounded,
              label: 'Filters',
              onPressed: isProcessing ? null : onFiltersToggle,
              isActive: showFilters,
            ),
            
            // Save Button
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    bool isSecondary = false,
    bool isActive = false,
  }) {
    return GestureDetector(
      onTap: onPressed != null 
          ? () {
              HapticFeedback.lightImpact();
              onPressed();
            }
          : null,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacing20,
          vertical: AppTheme.spacing12,
        ),
        decoration: BoxDecoration(
          color: isActive 
              ? AppTheme.brandPrimary.withValues(alpha: 0.3)
              : (isSecondary 
                  ? AppTheme.glassColor
                  : AppTheme.glassColor),
          borderRadius: BorderRadius.circular(AppTheme.radiusLG),
          border: Border.all(
            color: isActive 
                ? AppTheme.brandPrimary
                : AppTheme.glassBorderColor,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryDarkColor.withValues(alpha: 0.5),
              blurRadius: AppTheme.shadowBlurMD,
              offset: const Offset(0, AppTheme.spacing4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: onPressed != null 
                  ? (isActive 
                      ? AppTheme.brandPrimary
                      : AppTheme.textOnPrimaryColor)
                  : AppTheme.textOnPrimaryColor.withValues(alpha: 0.5),
              size: AppTheme.spacing24,
            ),
            const SizedBox(height: AppTheme.spacing4),
            Text(
              label,
              style: TextStyle(
                color: onPressed != null 
                    ? (isActive 
                        ? AppTheme.brandPrimary
                        : AppTheme.textOnPrimaryColor)
                    : AppTheme.textOnPrimaryColor.withValues(alpha: 0.5),
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return GestureDetector(
      onTap: isProcessing 
          ? null 
          : () {
              HapticFeedback.mediumImpact();
              onSave();
            },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacing24,
          vertical: AppTheme.spacing16,
        ),
        decoration: BoxDecoration(
          gradient: isProcessing 
              ? null
              : LinearGradient(
                  colors: AppTheme.primaryGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
          color: isProcessing 
              ? AppTheme.glassColor
              : null,
          borderRadius: BorderRadius.circular(AppTheme.radiusLG),
          border: Border.all(
            color: isProcessing 
                ? AppTheme.glassBorderColor
                : Colors.transparent,
            width: 1,
          ),
          boxShadow: [
            if (!isProcessing)
              BoxShadow(
                color: AppTheme.brandPrimary.withValues(alpha: 0.4),
                blurRadius: AppTheme.shadowBlurLG,
                offset: const Offset(0, AppTheme.spacing8),
              ),
            BoxShadow(
              color: AppTheme.primaryDarkColor.withValues(alpha: 0.5),
              blurRadius: AppTheme.shadowBlurMD,
              offset: const Offset(0, AppTheme.spacing4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isProcessing) ...[
              SizedBox(
                width: AppTheme.spacing16,
                height: AppTheme.spacing16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.textOnPrimaryColor.withValues(alpha: 0.7),
                  ),
                ),
              ),
              const SizedBox(width: AppTheme.spacing8),
            ] else ...[
              Icon(
                Icons.save_rounded,
                color: AppTheme.textOnPrimaryColor,
                size: AppTheme.spacing20,
              ),
              const SizedBox(width: AppTheme.spacing8),
            ],
            Text(
              isProcessing ? 'Processing...' : 'Save',
              style: TextStyle(
                color: isProcessing 
                    ? AppTheme.textOnPrimaryColor.withValues(alpha: 0.7)
                    : AppTheme.textOnPrimaryColor,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

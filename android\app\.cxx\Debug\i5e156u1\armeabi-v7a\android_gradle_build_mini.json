{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\DevGen\\lens_doc\\android\\app\\.cxx\\Debug\\i5e156u1\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\DevGen\\lens_doc\\android\\app\\.cxx\\Debug\\i5e156u1\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
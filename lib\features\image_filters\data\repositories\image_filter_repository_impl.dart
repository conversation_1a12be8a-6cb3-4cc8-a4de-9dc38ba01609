import 'dart:typed_data';
import 'package:image/image.dart' as img;
import '../../domain/repositories/image_filter_repository.dart';

class ImageFilterRepositoryImpl implements ImageFilterRepository {
  @override
  Future<Uint8List> applyFilter(
    Uint8List imageBytes, 
    FilterType filterType, 
    {Map<String, double>? parameters}
  ) async {
    switch (filterType) {
      case FilterType.original:
        return imageBytes;
      case FilterType.blackAndWhite:
        return await convertToBlackAndWhite(imageBytes);
      case FilterType.grayscale:
        return await _convertToGrayscale(imageBytes);
      case FilterType.autoEnhance:
        return await autoEnhance(imageBytes);
      case FilterType.highContrast:
        return await adjustContrast(imageBytes, 1.5);
      case FilterType.brightness:
        final brightness = parameters?['brightness'] ?? 1.2;
        return await adjustBrightness(imageBytes, brightness);
      case FilterType.sepia:
        return await _applyS<PERSON>ia(imageBytes);
      case FilterType.vintage:
        return await _applyVintage(imageBytes);
      case FilterType.sharp:
        return await sharpenImage(imageBytes);
    }
  }

  @override
  Future<Uint8List> adjustBrightness(Uint8List imageBytes, double brightness) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) throw Exception('Failed to decode image');

      final adjustedImage = img.adjustColor(image, brightness: brightness);
      return Uint8List.fromList(img.encodeJpg(adjustedImage, quality: 90));
    } catch (e) {
      throw Exception('Brightness adjustment failed: $e');
    }
  }

  @override
  Future<Uint8List> adjustContrast(Uint8List imageBytes, double contrast) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) throw Exception('Failed to decode image');

      final adjustedImage = img.adjustColor(image, contrast: contrast);
      return Uint8List.fromList(img.encodeJpg(adjustedImage, quality: 90));
    } catch (e) {
      throw Exception('Contrast adjustment failed: $e');
    }
  }

  @override
  Future<Uint8List> adjustSaturation(Uint8List imageBytes, double saturation) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) throw Exception('Failed to decode image');

      final adjustedImage = img.adjustColor(image, saturation: saturation);
      return Uint8List.fromList(img.encodeJpg(adjustedImage, quality: 90));
    } catch (e) {
      throw Exception('Saturation adjustment failed: $e');
    }
  }

  @override
  Future<Uint8List> autoEnhance(Uint8List imageBytes) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) throw Exception('Failed to decode image');

      // Apply multiple enhancements for document scanning
      var enhancedImage = image;
      
      // Increase contrast for better text readability
      enhancedImage = img.adjustColor(enhancedImage, contrast: 1.3);
      
      // Slight brightness adjustment
      enhancedImage = img.adjustColor(enhancedImage, brightness: 1.1);
      
      // Sharpen for better text clarity
      enhancedImage = img.convolution(enhancedImage, filter: [
        0, -1, 0,
        -1, 5, -1,
        0, -1, 0
      ]);

      return Uint8List.fromList(img.encodeJpg(enhancedImage, quality: 95));
    } catch (e) {
      throw Exception('Auto enhancement failed: $e');
    }
  }

  @override
  Future<Uint8List> convertToBlackAndWhite(Uint8List imageBytes) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) throw Exception('Failed to decode image');

      // Convert to grayscale first
      var bwImage = img.grayscale(image);
      
      // Apply threshold for pure black and white (good for documents)
      bwImage = img.adjustColor(bwImage, contrast: 2.0);
      
      return Uint8List.fromList(img.encodeJpg(bwImage, quality: 95));
    } catch (e) {
      throw Exception('Black and white conversion failed: $e');
    }
  }

  @override
  Future<Uint8List> sharpenImage(Uint8List imageBytes) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) throw Exception('Failed to decode image');

      // Apply sharpening filter
      final sharpenedImage = img.convolution(image, filter: [
        0, -1, 0,
        -1, 5, -1,
        0, -1, 0
      ]);

      return Uint8List.fromList(img.encodeJpg(sharpenedImage, quality: 95));
    } catch (e) {
      throw Exception('Image sharpening failed: $e');
    }
  }

  Future<Uint8List> _convertToGrayscale(Uint8List imageBytes) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) throw Exception('Failed to decode image');

      final grayscaleImage = img.grayscale(image);
      return Uint8List.fromList(img.encodeJpg(grayscaleImage, quality: 90));
    } catch (e) {
      throw Exception('Grayscale conversion failed: $e');
    }
  }

  Future<Uint8List> _applySepia(Uint8List imageBytes) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) throw Exception('Failed to decode image');

      final sepiaImage = img.sepia(image);
      return Uint8List.fromList(img.encodeJpg(sepiaImage, quality: 90));
    } catch (e) {
      throw Exception('Sepia filter failed: $e');
    }
  }

  Future<Uint8List> _applyVintage(Uint8List imageBytes) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) throw Exception('Failed to decode image');

      // Apply vintage effect (sepia + reduced saturation + slight vignette)
      var vintageImage = img.sepia(image);
      vintageImage = img.adjustColor(vintageImage, saturation: 0.7, brightness: 0.9);
      
      return Uint8List.fromList(img.encodeJpg(vintageImage, quality: 90));
    } catch (e) {
      throw Exception('Vintage filter failed: $e');
    }
  }
}

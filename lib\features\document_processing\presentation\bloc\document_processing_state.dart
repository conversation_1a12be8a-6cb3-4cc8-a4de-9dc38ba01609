import 'package:equatable/equatable.dart';
import 'dart:typed_data';
import 'package:flutter/material.dart';

abstract class DocumentProcessingState extends Equatable {
  const DocumentProcessingState();

  @override
  List<Object?> get props => [];
}

class DocumentProcessingInitial extends DocumentProcessingState {}

class DocumentProcessingLoading extends DocumentProcessingState {}

class EdgeDetectionCompleted extends DocumentProcessingState {
  final Uint8List originalImage;
  final List<Offset> detectedCorners;

  const EdgeDetectionCompleted({
    required this.originalImage,
    required this.detectedCorners,
  });

  @override
  List<Object?> get props => [originalImage, detectedCorners];
}

class CornersUpdated extends DocumentProcessingState {
  final Uint8List originalImage;
  final List<Offset> corners;

  const CornersUpdated({
    required this.originalImage,
    required this.corners,
  });

  @override
  List<Object?> get props => [originalImage, corners];
}

class ImageCropped extends DocumentProcessingState {
  final Uint8List originalImage;
  final Uint8List croppedImage;
  final List<Offset> corners;

  const ImageCropped({
    required this.originalImage,
    required this.croppedImage,
    required this.corners,
  });

  @override
  List<Object?> get props => [originalImage, croppedImage, corners];
}

class ImageEnhanced extends DocumentProcessingState {
  final Uint8List originalImage;
  final Uint8List enhancedImage;
  final List<Offset> corners;

  const ImageEnhanced({
    required this.originalImage,
    required this.enhancedImage,
    required this.corners,
  });

  @override
  List<Object?> get props => [originalImage, enhancedImage, corners];
}

class DocumentProcessingError extends DocumentProcessingState {
  final String message;

  const DocumentProcessingError({required this.message});

  @override
  List<Object?> get props => [message];
}

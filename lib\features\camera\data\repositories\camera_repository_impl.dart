import 'package:camera/camera.dart';
import 'dart:typed_data';
import '../../domain/repositories/camera_repository.dart';

class CameraRepositoryImpl implements CameraRepository {
  @override
  Future<List<CameraDescription>> getAvailableCameras() async {
    return await availableCameras();
  }

  @override
  Future<CameraController> initializeCamera(CameraDescription camera) async {
    final controller = CameraController(
      camera,
      ResolutionPreset.high,
      enableAudio: false,
    );
    
    await controller.initialize();
    return controller;
  }

  @override
  Future<Uint8List> captureImage(CameraController controller) async {
    if (!controller.value.isInitialized) {
      throw Exception('Camera not initialized');
    }
    
    final XFile image = await controller.takePicture();
    return await image.readAsBytes();
  }

  @override
  Future<void> disposeCamera(CameraController controller) async {
    await controller.dispose();
  }
}

import 'package:flutter/material.dart';
import 'dart:io';
import '../../../../core/theme/app_theme.dart';

class PremiumImageEditor extends StatefulWidget {
  final String imagePath;
  final String selectedFilter;
  final double brightness;
  final double contrast;
  final double saturation;

  const PremiumImageEditor({
    super.key,
    required this.imagePath,
    required this.selectedFilter,
    required this.brightness,
    required this.contrast,
    required this.saturation,
  });

  @override
  State<PremiumImageEditor> createState() => _PremiumImageEditorState();
}

class _PremiumImageEditorState extends State<PremiumImageEditor>
    with SingleTickerProviderStateMixin {
  late AnimationController _zoomController;

  double _scale = 1.0;
  double _previousScale = 1.0;
  Offset _offset = Offset.zero;
  Offset _previousOffset = Offset.zero;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _zoomController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _zoomController.dispose();
    super.dispose();
  }

  void _handleScaleStart(ScaleStartDetails details) {
    _previousScale = _scale;
    _previousOffset = _offset;
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    setState(() {
      _scale = (_previousScale * details.scale).clamp(0.5, 3.0);

      if (details.scale != 1.0) {
        _offset = _previousOffset + details.focalPointDelta;
      }
    });
  }

  void _handleScaleEnd(ScaleEndDetails details) {
    if (_scale < 1.0) {
      _resetZoom();
    }
  }

  void _resetZoom() {
    setState(() {
      _scale = 1.0;
      _offset = Offset.zero;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppTheme.primaryDarkColor,
      child: Center(
        child: GestureDetector(
          onScaleStart: _handleScaleStart,
          onScaleUpdate: _handleScaleUpdate,
          onScaleEnd: _handleScaleEnd,
          onDoubleTap: _resetZoom,
          child: Transform(
            transform:
                Matrix4.identity()
                  ..translate(_offset.dx, _offset.dy)
                  ..scale(_scale),
            child: Container(
              constraints: BoxConstraints(
                maxWidth:
                    MediaQuery.of(context).size.width - AppTheme.spacing32,
                maxHeight: MediaQuery.of(context).size.height * 0.7,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryDarkColor.withValues(alpha: 0.8),
                    blurRadius: AppTheme.shadowBlur2XL,
                    offset: const Offset(0, AppTheme.spacing16),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                child: _buildFilteredImage(),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFilteredImage() {
    return ColorFiltered(
      colorFilter: _getColorFilter(),
      child: Image.file(
        File(widget.imagePath),
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 200,
            height: 300,
            decoration: BoxDecoration(
              color: AppTheme.surfaceVariantColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusLG),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline_rounded,
                  size: AppTheme.spacing48,
                  color: AppTheme.errorColor,
                ),
                const SizedBox(height: AppTheme.spacing16),
                Text(
                  'Failed to load image',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  ColorFilter _getColorFilter() {
    // Base matrix for identity
    List<double> matrix = [
      1, 0, 0, 0, 0, // Red
      0, 1, 0, 0, 0, // Green
      0, 0, 1, 0, 0, // Blue
      0, 0, 0, 1, 0, // Alpha
    ];

    // Apply brightness
    final brightness = widget.brightness * 255;
    matrix[4] += brightness; // Red
    matrix[9] += brightness; // Green
    matrix[14] += brightness; // Blue

    // Apply contrast
    final contrast = widget.contrast + 1.0;
    matrix[0] *= contrast; // Red
    matrix[6] *= contrast; // Green
    matrix[12] *= contrast; // Blue

    // Apply filter-specific adjustments
    switch (widget.selectedFilter) {
      case 'bw':
        return ColorFilter.matrix(_getBWMatrix());
      case 'sepia':
        return ColorFilter.matrix(_getSepiaMatrix());
      case 'vintage':
        return ColorFilter.matrix(_getVintageMatrix());
      case 'cool':
        return ColorFilter.matrix(_getCoolMatrix());
      case 'warm':
        return ColorFilter.matrix(_getWarmMatrix());
      case 'vivid':
        return ColorFilter.matrix(_getVividMatrix());
      case 'original':
      default:
        return ColorFilter.matrix(matrix);
    }
  }

  List<double> _getBWMatrix() {
    return [
      0.299,
      0.587,
      0.114,
      0,
      0,
      0.299,
      0.587,
      0.114,
      0,
      0,
      0.299,
      0.587,
      0.114,
      0,
      0,
      0,
      0,
      0,
      1,
      0,
    ];
  }

  List<double> _getSepiaMatrix() {
    return [
      0.393,
      0.769,
      0.189,
      0,
      0,
      0.349,
      0.686,
      0.168,
      0,
      0,
      0.272,
      0.534,
      0.131,
      0,
      0,
      0,
      0,
      0,
      1,
      0,
    ];
  }

  List<double> _getVintageMatrix() {
    return [
      0.6,
      0.3,
      0.1,
      0,
      0,
      0.2,
      0.7,
      0.1,
      0,
      0,
      0.2,
      0.1,
      0.7,
      0,
      0,
      0,
      0,
      0,
      1,
      0,
    ];
  }

  List<double> _getCoolMatrix() {
    return [
      0.8,
      0.1,
      0.1,
      0,
      0,
      0.1,
      0.9,
      0.1,
      0,
      0,
      0.1,
      0.1,
      1.2,
      0,
      0,
      0,
      0,
      0,
      1,
      0,
    ];
  }

  List<double> _getWarmMatrix() {
    return [
      1.2,
      0.1,
      0.1,
      0,
      0,
      0.1,
      1.0,
      0.1,
      0,
      0,
      0.1,
      0.1,
      0.8,
      0,
      0,
      0,
      0,
      0,
      1,
      0,
    ];
  }

  List<double> _getVividMatrix() {
    return [1.4, 0, 0, 0, 0, 0, 1.4, 0, 0, 0, 0, 0, 1.4, 0, 0, 0, 0, 0, 1, 0];
  }
}

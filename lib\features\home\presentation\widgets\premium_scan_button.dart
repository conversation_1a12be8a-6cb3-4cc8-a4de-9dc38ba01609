import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumScanButton extends StatefulWidget {
  const PremiumScanButton({super.key});

  @override
  State<PremiumScanButton> createState() => _PremiumScanButtonState();
}

class _PremiumScanButtonState extends State<PremiumScanButton>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startPulseAnimation();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: AppTheme.animationFast,
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: AppTheme.curveStandard,
    ));
  }

  void _startPulseAnimation() {
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _scaleController.forward();
    HapticFeedback.lightImpact();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _scaleController.reverse();
    _handleScanTap();
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
    _scaleController.reverse();
  }

  void _handleScanTap() {
    HapticFeedback.mediumImpact();
    // Navigate to camera screen
    Navigator.of(context).pushNamed('/camera');
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          'Start Scanning',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppTheme.spacing8),
        Text(
          'Tap the button below to start scanning documents',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppTheme.textSecondaryColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppTheme.spacing32),
        
        // Main Scan Button
        AnimatedBuilder(
          animation: Listenable.merge([_pulseAnimation, _scaleAnimation]),
          builder: (context, child) {
            return Transform.scale(
              scale: _pulseAnimation.value * _scaleAnimation.value,
              child: GestureDetector(
                onTapDown: _onTapDown,
                onTapUp: _onTapUp,
                onTapCancel: _onTapCancel,
                child: Container(
                  width: AppTheme.spacing160,
                  height: AppTheme.spacing160,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: AppTheme.primaryGradient,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.brandPrimary.withValues(alpha: 0.4),
                        blurRadius: AppTheme.shadowBlur2XL,
                        offset: const Offset(0, AppTheme.spacing16),
                      ),
                      BoxShadow(
                        color: AppTheme.brandPrimary.withValues(alpha: 0.2),
                        blurRadius: AppTheme.shadowBlur3XL,
                        offset: const Offset(0, AppTheme.spacing24),
                      ),
                      if (_isPressed)
                        BoxShadow(
                          color: AppTheme.brandPrimary.withValues(alpha: 0.6),
                          blurRadius: AppTheme.shadowBlur2XL,
                          offset: const Offset(0, AppTheme.spacing8),
                        ),
                    ],
                  ),
                  child: Container(
                    margin: const EdgeInsets.all(AppTheme.spacing8),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppTheme.glassColor,
                      border: Border.all(
                        color: AppTheme.glassBorderColor,
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(AppTheme.spacing16),
                            decoration: BoxDecoration(
                              color: AppTheme.glassColor,
                              borderRadius: BorderRadius.circular(AppTheme.radius2XL),
                              border: Border.all(
                                color: AppTheme.glassBorderColor,
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              Icons.document_scanner_rounded,
                              size: AppTheme.spacing48,
                              color: AppTheme.textOnPrimaryColor,
                            ),
                          ),
                          const SizedBox(height: AppTheme.spacing12),
                          Text(
                            'SCAN',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: AppTheme.textOnPrimaryColor,
                              fontWeight: FontWeight.w700,
                              letterSpacing: 2.0,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
        
        const SizedBox(height: AppTheme.spacing24),
        
        // Scan Options
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildScanOption(
              icon: Icons.camera_alt_outlined,
              label: 'Camera',
              onTap: () => Navigator.of(context).pushNamed('/camera'),
            ),
            _buildScanOption(
              icon: Icons.photo_library_outlined,
              label: 'Gallery',
              onTap: () {
                // Import from gallery
              },
            ),
            _buildScanOption(
              icon: Icons.qr_code_scanner_outlined,
              label: 'QR Code',
              onTap: () {
                // QR code scanner
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildScanOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacing20,
          vertical: AppTheme.spacing16,
        ),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(AppTheme.radius2XL),
          border: Border.all(
            color: AppTheme.outlineColor,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
              blurRadius: AppTheme.shadowBlurMD,
              offset: const Offset(0, AppTheme.spacing4),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacing12),
              decoration: BoxDecoration(
                color: AppTheme.brandPrimary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusMD),
              ),
              child: Icon(
                icon,
                color: AppTheme.brandPrimary,
                size: AppTheme.spacing24,
              ),
            ),
            const SizedBox(height: AppTheme.spacing8),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

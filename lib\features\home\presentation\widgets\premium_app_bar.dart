import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumAppBar extends StatelessWidget {
  const PremiumAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      expandedHeight: AppTheme.spacing112,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: AppTheme.backgroundColor,
      surfaceTintColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppTheme.backgroundColor,
                AppTheme.backgroundSecondary,
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing20,
                vertical: AppTheme.spacing16,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Logo and Title
                      Row(
                        children: [
                          Container(
                            width: AppTheme.spacing40,
                            height: AppTheme.spacing40,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: AppTheme.primaryGradient,
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(AppTheme.radiusMD),
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.brandPrimary.withValues(alpha: 0.3),
                                  blurRadius: AppTheme.shadowBlurMD,
                                  offset: const Offset(0, AppTheme.spacing4),
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.document_scanner_rounded,
                              color: AppTheme.textOnPrimaryColor,
                              size: AppTheme.spacing20,
                            ),
                          ),
                          const SizedBox(width: AppTheme.spacing12),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'LensDoc',
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  color: AppTheme.textPrimaryColor,
                                  letterSpacing: -0.5,
                                ),
                              ),
                              Text(
                                'Pro Scanner',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: AppTheme.textSecondaryColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      
                      // Action Buttons
                      Row(
                        children: [
                          _buildActionButton(
                            icon: Icons.search_rounded,
                            onPressed: () {
                              // Navigate to search
                            },
                          ),
                          const SizedBox(width: AppTheme.spacing8),
                          _buildActionButton(
                            icon: Icons.notifications_outlined,
                            onPressed: () {
                              // Show notifications
                            },
                            hasNotification: true,
                          ),
                          const SizedBox(width: AppTheme.spacing8),
                          _buildProfileButton(),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    bool hasNotification = false,
  }) {
    return Container(
      width: AppTheme.spacing40,
      height: AppTheme.spacing40,
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusMD),
        border: Border.all(
          color: AppTheme.outlineColor,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
            blurRadius: AppTheme.shadowBlurSM,
            offset: const Offset(0, AppTheme.spacing2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppTheme.radiusMD),
          onTap: onPressed,
          child: Stack(
            children: [
              Center(
                child: Icon(
                  icon,
                  color: AppTheme.textSecondaryColor,
                  size: AppTheme.spacing20,
                ),
              ),
              if (hasNotification)
                Positioned(
                  top: AppTheme.spacing8,
                  right: AppTheme.spacing8,
                  child: Container(
                    width: AppTheme.spacing8,
                    height: AppTheme.spacing8,
                    decoration: BoxDecoration(
                      color: AppTheme.errorColor,
                      borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                      border: Border.all(
                        color: AppTheme.surfaceColor,
                        width: 1,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileButton() {
    return Container(
      width: AppTheme.spacing40,
      height: AppTheme.spacing40,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: AppTheme.secondaryGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusMD),
        boxShadow: [
          BoxShadow(
            color: AppTheme.secondaryColor.withValues(alpha: 0.3),
            blurRadius: AppTheme.shadowBlurMD,
            offset: const Offset(0, AppTheme.spacing4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppTheme.radiusMD),
          onTap: () {
            // Navigate to profile/settings
          },
          child: Center(
            child: Text(
              'U',
              style: TextStyle(
                color: AppTheme.textOnPrimaryColor,
                fontSize: AppTheme.spacing16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

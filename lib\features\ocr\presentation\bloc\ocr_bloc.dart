import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/extract_text.dart';
import '../../data/repositories/ocr_repository_impl.dart';
import 'ocr_event.dart';
import 'ocr_state.dart';

class OcrBloc extends Bloc<OcrEvent, OcrState> {
  final ExtractText extractText;
  final OcrRepositoryImpl _repository = OcrRepositoryImpl();

  OcrBloc({
    required this.extractText,
  }) : super(OcrInitial()) {
    on<ExtractTextEvent>(_onExtractText);
    on<ExtractStructuredTextEvent>(_onExtractStructuredText);
    on<ClearOcrResultsEvent>(_onClearResults);
  }

  Future<void> _onExtractText(
    ExtractTextEvent event,
    Emitter<OcrState> emit,
  ) async {
    try {
      emit(OcrLoading());
      
      final text = await extractText(event.imageBytes);
      
      emit(OcrTextExtracted(extractedText: text));
    } catch (e) {
      emit(OcrError(message: e.toString()));
    }
  }

  Future<void> _onExtractStructuredText(
    ExtractStructuredTextEvent event,
    Emitter<OcrState> emit,
  ) async {
    try {
      emit(OcrLoading());
      
      final structuredData = await _repository.extractStructuredText(event.imageBytes);
      
      emit(OcrStructuredTextExtracted(structuredData: structuredData));
    } catch (e) {
      emit(OcrError(message: e.toString()));
    }
  }

  void _onClearResults(
    ClearOcrResultsEvent event,
    Emitter<OcrState> emit,
  ) {
    emit(OcrInitial());
  }

  @override
  Future<void> close() {
    _repository.dispose();
    return super.close();
  }
}

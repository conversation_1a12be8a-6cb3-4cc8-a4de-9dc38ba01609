import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumSettingsScreen extends StatefulWidget {
  const PremiumSettingsScreen({super.key});

  @override
  State<PremiumSettingsScreen> createState() => _PremiumSettingsScreenState();
}

class _PremiumSettingsScreenState extends State<PremiumSettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startEntryAnimation();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: AppTheme.curveEmphasized,
    ));
  }

  void _startEntryAnimation() {
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacing20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Text(
                  'Settings',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                
                const SizedBox(height: AppTheme.spacing8),
                
                Text(
                  'Customize your LensDoc experience',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                
                const SizedBox(height: AppTheme.spacing32),
                
                // Settings Sections
                Expanded(
                  child: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: Column(
                      children: [
                        _buildSettingsSection(
                          title: 'Camera',
                          items: [
                            _buildSettingsItem(
                              icon: Icons.camera_alt_rounded,
                              title: 'Auto Enhancement',
                              subtitle: 'Automatically enhance scanned documents',
                              trailing: Switch.adaptive(
                                value: true,
                                onChanged: (value) {
                                  HapticFeedback.lightImpact();
                                },
                                activeColor: AppTheme.brandPrimary,
                              ),
                            ),
                            _buildSettingsItem(
                              icon: Icons.grid_3x3_outlined,
                              title: 'Grid Lines',
                              subtitle: 'Show composition grid in camera',
                              trailing: Switch.adaptive(
                                value: false,
                                onChanged: (value) {
                                  HapticFeedback.lightImpact();
                                },
                                activeColor: AppTheme.brandPrimary,
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: AppTheme.spacing24),
                        
                        _buildSettingsSection(
                          title: 'Storage',
                          items: [
                            _buildSettingsItem(
                              icon: Icons.cloud_outlined,
                              title: 'Cloud Sync',
                              subtitle: 'Sync documents to cloud storage',
                              trailing: Switch.adaptive(
                                value: true,
                                onChanged: (value) {
                                  HapticFeedback.lightImpact();
                                },
                                activeColor: AppTheme.brandPrimary,
                              ),
                            ),
                            _buildSettingsItem(
                              icon: Icons.storage_rounded,
                              title: 'Storage Location',
                              subtitle: 'Choose where to save documents',
                              trailing: Icon(
                                Icons.chevron_right_rounded,
                                color: AppTheme.textSecondaryColor,
                              ),
                              onTap: () {
                                HapticFeedback.lightImpact();
                              },
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: AppTheme.spacing24),
                        
                        _buildSettingsSection(
                          title: 'About',
                          items: [
                            _buildSettingsItem(
                              icon: Icons.info_outline_rounded,
                              title: 'App Version',
                              subtitle: '1.0.0',
                              trailing: Icon(
                                Icons.chevron_right_rounded,
                                color: AppTheme.textSecondaryColor,
                              ),
                              onTap: () {
                                HapticFeedback.lightImpact();
                              },
                            ),
                            _buildSettingsItem(
                              icon: Icons.privacy_tip_outlined,
                              title: 'Privacy Policy',
                              subtitle: 'View our privacy policy',
                              trailing: Icon(
                                Icons.chevron_right_rounded,
                                color: AppTheme.textSecondaryColor,
                              ),
                              onTap: () {
                                HapticFeedback.lightImpact();
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required List<Widget> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title.toUpperCase(),
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
            color: AppTheme.textSecondaryColor,
            fontWeight: FontWeight.w600,
            letterSpacing: 1.2,
          ),
        ),
        
        const SizedBox(height: AppTheme.spacing12),
        
        Container(
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusXL),
            border: Border.all(
              color: AppTheme.outlineColor,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
                blurRadius: AppTheme.shadowBlurMD,
                offset: const Offset(0, AppTheme.spacing4),
              ),
            ],
          ),
          child: Column(
            children: items,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Widget trailing,
    VoidCallback? onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(AppTheme.radiusXL),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacing16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacing10),
                decoration: BoxDecoration(
                  color: AppTheme.brandPrimary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMD),
                ),
                child: Icon(
                  icon,
                  color: AppTheme.brandPrimary,
                  size: AppTheme.spacing20,
                ),
              ),
              
              const SizedBox(width: AppTheme.spacing16),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacing2),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              
              trailing,
            ],
          ),
        ),
      ),
    );
  }
}

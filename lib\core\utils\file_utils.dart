import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../constants/app_constants.dart';

class FileUtils {
  /// Get the app's documents directory
  static Future<Directory> getAppDocumentsDirectory() async {
    final Directory appDocDir = await getApplicationDocumentsDirectory();
    final Directory lensDocDir = Directory(path.join(appDocDir.path, AppConstants.documentsFolder));
    
    if (!await lensDocDir.exists()) {
      await lensDocDir.create(recursive: true);
    }
    
    return lensDocDir;
  }
  
  /// Get the scanned images directory
  static Future<Directory> getScannedImagesDirectory() async {
    final Directory appDir = await getAppDocumentsDirectory();
    final Directory imagesDir = Directory(path.join(appDir.path, AppConstants.scannedImagesFolder));
    
    if (!await imagesDir.exists()) {
      await imagesDir.create(recursive: true);
    }
    
    return imagesDir;
  }
  
  /// Get the PDFs directory
  static Future<Directory> getPdfsDirectory() async {
    final Directory appDir = await getAppDocumentsDirectory();
    final Directory pdfsDir = Directory(path.join(appDir.path, AppConstants.pdfFolder));
    
    if (!await pdfsDir.exists()) {
      await pdfsDir.create(recursive: true);
    }
    
    return pdfsDir;
  }
  
  /// Generate a unique filename with timestamp
  static String generateUniqueFileName(String extension) {
    final DateTime now = DateTime.now();
    final String timestamp = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}';
    return 'scan_$timestamp$extension';
  }
  
  /// Save image bytes to file
  static Future<File> saveImageToFile(Uint8List imageBytes, String fileName) async {
    final Directory imagesDir = await getScannedImagesDirectory();
    final File imageFile = File(path.join(imagesDir.path, fileName));
    return await imageFile.writeAsBytes(imageBytes);
  }
  
  /// Save PDF bytes to file
  static Future<File> savePdfToFile(Uint8List pdfBytes, String fileName) async {
    final Directory pdfsDir = await getPdfsDirectory();
    final File pdfFile = File(path.join(pdfsDir.path, fileName));
    return await pdfFile.writeAsBytes(pdfBytes);
  }
  
  /// Get file size in MB
  static double getFileSizeInMB(File file) {
    final int bytes = file.lengthSync();
    return bytes / (1024 * 1024);
  }
  
  /// Delete file if exists
  static Future<bool> deleteFile(String filePath) async {
    try {
      final File file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
  
  /// Get all scanned images
  static Future<List<File>> getAllScannedImages() async {
    final Directory imagesDir = await getScannedImagesDirectory();
    final List<FileSystemEntity> entities = imagesDir.listSync();
    
    return entities
        .whereType<File>()
        .where((file) => 
            file.path.toLowerCase().endsWith(AppConstants.jpgExtension) ||
            file.path.toLowerCase().endsWith(AppConstants.pngExtension))
        .toList();
  }
  
  /// Get all PDFs
  static Future<List<File>> getAllPdfs() async {
    final Directory pdfsDir = await getPdfsDirectory();
    final List<FileSystemEntity> entities = pdfsDir.listSync();
    
    return entities
        .whereType<File>()
        .where((file) => file.path.toLowerCase().endsWith(AppConstants.pdfExtension))
        .toList();
  }
}

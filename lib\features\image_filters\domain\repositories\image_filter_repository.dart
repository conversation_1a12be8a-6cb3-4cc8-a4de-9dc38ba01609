import 'dart:typed_data';

enum FilterType {
  original,
  blackAndWhite,
  grayscale,
  autoEnhance,
  highContrast,
  brightness,
  sepia,
  vintage,
  sharp,
}

abstract class ImageFilterRepository {
  Future<Uint8List> applyFilter(Uint8List imageBytes, FilterType filterType, {Map<String, double>? parameters});
  Future<Uint8List> adjustBrightness(Uint8List imageBytes, double brightness);
  Future<Uint8List> adjustContrast(Uint8List imageBytes, double contrast);
  Future<Uint8List> adjustSaturation(Uint8List imageBytes, double saturation);
  Future<Uint8List> autoEnhance(Uint8List imageBytes);
  Future<Uint8List> convertToBlackAndWhite(Uint8List imageBytes);
  Future<Uint8List> sharpenImage(Uint8List imageBytes);
}

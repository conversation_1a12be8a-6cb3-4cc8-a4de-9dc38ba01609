import 'package:flutter/material.dart';
import 'dart:typed_data';
import '../../../../core/theme/app_theme.dart';

import '../../domain/repositories/image_filter_repository.dart';
import '../../data/repositories/image_filter_repository_impl.dart';
import '../widgets/filter_preview_widget.dart';
import '../widgets/adjustment_slider.dart';

class ImageEditorScreen extends StatefulWidget {
  final Uint8List originalImage;

  const ImageEditorScreen({super.key, required this.originalImage});

  @override
  State<ImageEditorScreen> createState() => _ImageEditorScreenState();
}

class _ImageEditorScreenState extends State<ImageEditorScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ImageFilterRepository _filterRepository = ImageFilterRepositoryImpl();

  Uint8List? _currentImage;
  bool _isProcessing = false;

  // Filter settings
  FilterType _selectedFilter = FilterType.original;

  // Adjustment settings
  double _brightness = 1.0;
  double _contrast = 1.0;
  double _saturation = 1.0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _currentImage = widget.originalImage;
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.textPrimaryColor,
      appBar: AppBar(
        title: Text(
          'Edit Image',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: AppTheme.textOnPrimaryColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.textPrimaryColor,
        foregroundColor: AppTheme.textOnPrimaryColor,
        actions: [
          if (_isProcessing)
            Container(
              margin: const EdgeInsets.all(AppTheme.spacing8),
              child: SizedBox(
                width: AppTheme.spacing24,
                height: AppTheme.spacing24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.textOnPrimaryColor,
                  ),
                ),
              ),
            )
          else
            IconButton(
              onPressed: _saveImage,
              icon: const Icon(Icons.check),
              tooltip: 'Save changes',
            ),
        ],
      ),
      body: Column(
        children: [
          // Image preview
          Expanded(
            flex: 3,
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacing16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                border: Border.all(
                  color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppTheme.radiusLG - 1),
                child: Image.memory(
                  _currentImage ?? widget.originalImage,
                  fit: BoxFit.contain,
                  width: double.infinity,
                ),
              ),
            ),
          ),

          // Controls
          Expanded(
            flex: 2,
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.surfaceColor,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(AppTheme.radiusLG),
                ),
              ),
              child: Column(
                children: [
                  // Tab bar
                  Container(
                    margin: const EdgeInsets.all(AppTheme.spacing16),
                    decoration: BoxDecoration(
                      color: AppTheme.surfaceVariantColor,
                      borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                    ),
                    child: TabBar(
                      controller: _tabController,
                      indicator: BoxDecoration(
                        color: AppTheme.primaryColor,
                        borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                      ),
                      labelColor: AppTheme.textOnPrimaryColor,
                      unselectedLabelColor: AppTheme.textSecondaryColor,
                      tabs: const [
                        Tab(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.filter_vintage_outlined, size: 18),
                              SizedBox(width: AppTheme.spacing8),
                              Text('Filters'),
                            ],
                          ),
                        ),
                        Tab(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.tune_outlined, size: 18),
                              SizedBox(width: AppTheme.spacing8),
                              Text('Adjust'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Tab content
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [_buildFiltersTab(), _buildAdjustmentsTab()],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersTab() {
    final filters = [
      {
        'type': FilterType.original,
        'name': 'Original',
        'icon': Icons.image_outlined,
      },
      {
        'type': FilterType.autoEnhance,
        'name': 'Auto',
        'icon': Icons.auto_fix_high_outlined,
      },
      {
        'type': FilterType.blackAndWhite,
        'name': 'B&W',
        'icon': Icons.contrast_outlined,
      },
      {
        'type': FilterType.grayscale,
        'name': 'Gray',
        'icon': Icons.filter_b_and_w_outlined,
      },
      {
        'type': FilterType.highContrast,
        'name': 'Contrast',
        'icon': Icons.contrast_outlined,
      },
      {
        'type': FilterType.sharp,
        'name': 'Sharp',
        'icon': Icons.details_outlined,
      },
      {
        'type': FilterType.sepia,
        'name': 'Sepia',
        'icon': Icons.filter_vintage_outlined,
      },
      {
        'type': FilterType.vintage,
        'name': 'Vintage',
        'icon': Icons.camera_outlined,
      },
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choose a filter',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppTheme.spacing16),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: AppTheme.spacing12,
                mainAxisSpacing: AppTheme.spacing12,
                childAspectRatio: 0.8,
              ),
              itemCount: filters.length,
              itemBuilder: (context, index) {
                final filter = filters[index];
                final filterType = filter['type'] as FilterType;
                final isSelected = _selectedFilter == filterType;

                return FilterPreviewWidget(
                  filterType: filterType,
                  name: filter['name'] as String,
                  icon: filter['icon'] as IconData,
                  isSelected: isSelected,
                  onTap: () => _applyFilter(filterType),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdjustmentsTab() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Adjust image',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppTheme.spacing24),

          AdjustmentSlider(
            label: 'Brightness',
            value: _brightness,
            min: 0.5,
            max: 2.0,
            onChanged: (value) {
              setState(() {
                _brightness = value;
              });
              _applyAdjustments();
            },
            icon: Icons.brightness_6_outlined,
          ),

          const SizedBox(height: AppTheme.spacing20),

          AdjustmentSlider(
            label: 'Contrast',
            value: _contrast,
            min: 0.5,
            max: 2.0,
            onChanged: (value) {
              setState(() {
                _contrast = value;
              });
              _applyAdjustments();
            },
            icon: Icons.contrast_outlined,
          ),

          const SizedBox(height: AppTheme.spacing20),

          AdjustmentSlider(
            label: 'Saturation',
            value: _saturation,
            min: 0.0,
            max: 2.0,
            onChanged: (value) {
              setState(() {
                _saturation = value;
              });
              _applyAdjustments();
            },
            icon: Icons.palette_outlined,
          ),

          const Spacer(),

          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _resetAdjustments,
                  child: const Text('Reset'),
                ),
              ),
              const SizedBox(width: AppTheme.spacing12),
              Expanded(
                child: ElevatedButton(
                  onPressed: _applyAdjustments,
                  child: const Text('Apply'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _applyFilter(FilterType filterType) async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
      _selectedFilter = filterType;
    });

    try {
      final filteredImage = await _filterRepository.applyFilter(
        widget.originalImage,
        filterType,
      );

      setState(() {
        _currentImage = filteredImage;
        _isProcessing = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error applying filter: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _applyAdjustments() async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      var adjustedImage = widget.originalImage;

      if (_brightness != 1.0) {
        adjustedImage = await _filterRepository.adjustBrightness(
          adjustedImage,
          _brightness,
        );
      }

      if (_contrast != 1.0) {
        adjustedImage = await _filterRepository.adjustContrast(
          adjustedImage,
          _contrast,
        );
      }

      if (_saturation != 1.0) {
        adjustedImage = await _filterRepository.adjustSaturation(
          adjustedImage,
          _saturation,
        );
      }

      setState(() {
        _currentImage = adjustedImage;
        _isProcessing = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error applying adjustments: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _resetAdjustments() {
    setState(() {
      _brightness = 1.0;
      _contrast = 1.0;
      _saturation = 1.0;
      _currentImage = widget.originalImage;
    });
  }

  void _saveImage() {
    Navigator.of(context).pop(_currentImage ?? widget.originalImage);
  }
}

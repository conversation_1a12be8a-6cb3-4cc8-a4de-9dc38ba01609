import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import '../../domain/repositories/document_processing_repository.dart';

class DocumentProcessingRepositoryImpl implements DocumentProcessingRepository {
  @override
  Future<List<Offset>> detectEdges(Uint8List imageBytes) async {
    try {
      // Phase 1: Simple edge detection using image analysis
      // This provides a good starting point and can be enhanced later
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      // Simple edge detection: find the largest rectangular area
      // For Phase 1, we'll use a smart default with some basic analysis
      final corners = _findDocumentCorners(image);

      return corners.map((corner) => Offset(corner.dx, corner.dy)).toList();
    } catch (e) {
      throw Exception('Edge detection failed: $e');
    }
  }

  List<Offset> _findDocumentCorners(img.Image image) {
    final width = image.width.toDouble();
    final height = image.height.toDouble();

    // For Phase 1, use intelligent defaults based on common document ratios
    // This can be enhanced with actual edge detection algorithms later

    // Check if image is landscape or portrait
    final isLandscape = width > height;

    // Adjust margins based on orientation and aspect ratio
    double horizontalMargin, verticalMargin;

    if (isLandscape) {
      // Landscape: likely a document photo, use smaller margins
      horizontalMargin = 0.08;
      verticalMargin = 0.12;
    } else {
      // Portrait: likely a phone photo of document, use larger margins
      horizontalMargin = 0.12;
      verticalMargin = 0.08;
    }

    // Calculate corners with smart margins
    final left = width * horizontalMargin;
    final right = width * (1 - horizontalMargin);
    final top = height * verticalMargin;
    final bottom = height * (1 - verticalMargin);

    return [
      Offset(left, top), // Top-left
      Offset(right, top), // Top-right
      Offset(right, bottom), // Bottom-right
      Offset(left, bottom), // Bottom-left
    ];
  }

  @override
  Future<Uint8List> cropImage(
    Uint8List imageBytes,
    List<Offset> corners,
  ) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      // For Phase 1, perform a simple rectangular crop
      // In later phases, this will be enhanced with perspective correction

      // Find bounding rectangle of the corners
      double minX = corners.map((c) => c.dx).reduce((a, b) => a < b ? a : b);
      double maxX = corners.map((c) => c.dx).reduce((a, b) => a > b ? a : b);
      double minY = corners.map((c) => c.dy).reduce((a, b) => a < b ? a : b);
      double maxY = corners.map((c) => c.dy).reduce((a, b) => a > b ? a : b);

      // Ensure coordinates are within image bounds
      minX = minX.clamp(0, image.width.toDouble());
      maxX = maxX.clamp(0, image.width.toDouble());
      minY = minY.clamp(0, image.height.toDouble());
      maxY = maxY.clamp(0, image.height.toDouble());

      final cropWidth = (maxX - minX).toInt();
      final cropHeight = (maxY - minY).toInt();

      if (cropWidth <= 0 || cropHeight <= 0) {
        throw Exception('Invalid crop dimensions');
      }

      // Crop the image
      final croppedImage = img.copyCrop(
        image,
        x: minX.toInt(),
        y: minY.toInt(),
        width: cropWidth,
        height: cropHeight,
      );

      // Encode back to bytes
      final encodedImage = img.encodeJpg(croppedImage, quality: 85);
      return Uint8List.fromList(encodedImage);
    } catch (e) {
      throw Exception('Image cropping failed: $e');
    }
  }

  @override
  Future<Uint8List> enhanceImage(Uint8List imageBytes) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      // Apply basic enhancements
      var enhancedImage = image;

      // Increase contrast
      enhancedImage = img.contrast(enhancedImage, contrast: 1.2);

      // Adjust brightness slightly (using adjustColor instead of deprecated brightness)
      enhancedImage = img.adjustColor(enhancedImage, brightness: 1.1);

      // Sharpen the image
      enhancedImage = img.convolution(
        enhancedImage,
        filter: [0, -1, 0, -1, 5, -1, 0, -1, 0],
      );

      // Encode back to bytes
      final encodedImage = img.encodeJpg(enhancedImage, quality: 90);
      return Uint8List.fromList(encodedImage);
    } catch (e) {
      throw Exception('Image enhancement failed: $e');
    }
  }
}

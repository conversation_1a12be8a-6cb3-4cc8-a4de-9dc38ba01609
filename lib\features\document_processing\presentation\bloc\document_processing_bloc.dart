import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import '../../domain/usecases/detect_edges.dart';
import '../../domain/usecases/crop_image.dart';
import 'document_processing_event.dart';
import 'document_processing_state.dart';

class DocumentProcessingBloc
    extends Bloc<DocumentProcessingEvent, DocumentProcessingState> {
  final DetectEdges detectEdges;
  final CropImage cropImage;

  DocumentProcessingBloc({required this.detectEdges, required this.cropImage})
    : super(DocumentProcessingInitial()) {
    on<ProcessImageEvent>(_onProcessImage);
    on<DetectEdgesEvent>(_onDetectEdges);
    on<CropImageEvent>(_onCropImage);
    on<UpdateCornersEvent>(_onUpdateCorners);
    on<EnhanceImageEvent>(_onEnhanceImage);
  }

  Future<void> _onProcessImage(
    ProcessImageEvent event,
    Emitter<DocumentProcessingState> emit,
  ) async {
    try {
      emit(DocumentProcessingLoading());

      // Automatically detect edges when processing starts
      final corners = await detectEdges(event.imageBytes);

      emit(
        EdgeDetectionCompleted(
          originalImage: event.imageBytes,
          detectedCorners: corners,
        ),
      );
    } catch (e) {
      emit(DocumentProcessingError(message: e.toString()));
    }
  }

  Future<void> _onDetectEdges(
    DetectEdgesEvent event,
    Emitter<DocumentProcessingState> emit,
  ) async {
    try {
      emit(DocumentProcessingLoading());

      final corners = await detectEdges(event.imageBytes);

      emit(
        EdgeDetectionCompleted(
          originalImage: event.imageBytes,
          detectedCorners: corners,
        ),
      );
    } catch (e) {
      emit(DocumentProcessingError(message: e.toString()));
    }
  }

  Future<void> _onCropImage(
    CropImageEvent event,
    Emitter<DocumentProcessingState> emit,
  ) async {
    try {
      emit(DocumentProcessingLoading());

      final croppedImageBytes = await cropImage(
        event.imageBytes,
        event.corners,
      );

      emit(
        ImageCropped(
          originalImage: event.imageBytes,
          croppedImage: croppedImageBytes,
          corners: event.corners,
        ),
      );
    } catch (e) {
      emit(DocumentProcessingError(message: e.toString()));
    }
  }

  Future<void> _onUpdateCorners(
    UpdateCornersEvent event,
    Emitter<DocumentProcessingState> emit,
  ) async {
    final currentState = state;
    if (currentState is EdgeDetectionCompleted) {
      emit(
        CornersUpdated(
          originalImage: currentState.originalImage,
          corners: event.corners,
        ),
      );
    } else if (currentState is CornersUpdated) {
      emit(
        CornersUpdated(
          originalImage: currentState.originalImage,
          corners: event.corners,
        ),
      );
    }
  }

  Future<void> _onEnhanceImage(
    EnhanceImageEvent event,
    Emitter<DocumentProcessingState> emit,
  ) async {
    try {
      emit(DocumentProcessingLoading());

      // For Phase 1, we'll use the basic enhancement from the repository
      // In later phases, this can be expanded with more sophisticated filters
      final currentState = state;
      List<Offset> corners = [];

      if (currentState is ImageCropped) {
        corners = currentState.corners;
      } else if (currentState is EdgeDetectionCompleted) {
        corners = currentState.detectedCorners;
      }

      // For now, just return the enhanced image
      // This will be expanded in later phases
      emit(
        ImageEnhanced(
          originalImage: event.imageBytes,
          enhancedImage: event.imageBytes, // Placeholder for Phase 1
          corners: corners,
        ),
      );
    } catch (e) {
      emit(DocumentProcessingError(message: e.toString()));
    }
  }
}

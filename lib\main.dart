import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'core/di/injection_container.dart' as di;
import 'core/theme/app_theme.dart';
import 'features/home/<USER>/pages/home_screen.dart';
import 'features/camera/presentation/pages/camera_screen.dart';
import 'features/document_processing/presentation/pages/document_processing_screen.dart';
import 'features/document_processing/presentation/pages/document_preview_screen.dart';
import 'features/file_manager/presentation/pages/file_manager_screen.dart';
import 'features/settings/presentation/pages/settings_screen.dart';
import 'features/image_filters/presentation/pages/image_editor_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection
  await di.init();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const LensDocApp());
}

class LensDocApp extends StatelessWidget {
  const LensDocApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'LensDoc',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: const HomeScreen(),
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/':
            return MaterialPageRoute(builder: (context) => const HomeScreen());
          case '/camera':
            return MaterialPageRoute(
              builder: (context) => const CameraScreen(),
            );
          case '/document-processing':
            final imageBytes = settings.arguments as Uint8List;
            return MaterialPageRoute(
              builder:
                  (context) => DocumentProcessingScreen(imageBytes: imageBytes),
            );
          case '/document-preview':
            final pages = settings.arguments as List<Uint8List>;
            return MaterialPageRoute(
              builder: (context) => DocumentPreviewScreen(pages: pages),
            );
          case '/file-manager':
            return MaterialPageRoute(
              builder: (context) => const FileManagerScreen(),
            );
          case '/settings':
            return MaterialPageRoute(
              builder: (context) => const SettingsScreen(),
            );
          case '/image-editor':
            final imageBytes = settings.arguments as Uint8List;
            return MaterialPageRoute(
              builder:
                  (context) => ImageEditorScreen(originalImage: imageBytes),
            );
          default:
            return MaterialPageRoute(builder: (context) => const HomeScreen());
        }
      },
      debugShowCheckedModeBanner: false,
    );
  }
}

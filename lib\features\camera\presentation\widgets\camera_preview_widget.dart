import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import '../../../../core/theme/app_theme.dart';

class CameraPreviewWidget extends StatelessWidget {
  final CameraController controller;

  const CameraPreviewWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    if (!controller.value.isInitialized) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    return Container(
      margin: const EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppTheme.radiusLG),
        border: Border.all(color: AppTheme.textOnPrimaryColor, width: 2),
        boxShadow: [
          BoxShadow(
            color: AppTheme.textPrimaryColor.withValues(alpha: 0.3),
            blurRadius: AppTheme.spacing16,
            offset: const Offset(0, AppTheme.spacing8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppTheme.radiusLG - 2),
        child: Stack(
          children: [
            // Camera Preview
            AspectRatio(
              aspectRatio: controller.value.aspectRatio,
              child: CameraPreview(controller),
            ),

            // Document Detection Overlay
            Positioned.fill(
              child: CustomPaint(painter: DocumentOverlayPainter()),
            ),

            // Scanning Instructions
            Positioned(
              bottom: AppTheme.spacing16,
              left: AppTheme.spacing16,
              right: AppTheme.spacing16,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing16,
                  vertical: AppTheme.spacing12,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.textPrimaryColor.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                  border: Border.all(
                    color: AppTheme.textOnPrimaryColor.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Text(
                  'Position document within the frame',
                  style: TextStyle(
                    color: AppTheme.textOnPrimaryColor,
                    fontSize: AppTheme.fontSizeSM,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DocumentOverlayPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = AppTheme.primaryLightColor.withValues(alpha: 0.9)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3.0;

    final shadowPaint =
        Paint()
          ..color = AppTheme.textPrimaryColor.withValues(alpha: 0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 5.0;

    // Draw corner brackets to indicate document detection area
    final cornerLength = AppTheme.spacing32;
    final margin = size.width * 0.1;

    // Helper function to draw corner with shadow
    void drawCorner(Offset corner, Offset horizontal, Offset vertical) {
      // Draw shadow first
      canvas.drawLine(corner, horizontal, shadowPaint);
      canvas.drawLine(corner, vertical, shadowPaint);
      // Draw main line on top
      canvas.drawLine(corner, horizontal, paint);
      canvas.drawLine(corner, vertical, paint);
    }

    // Top-left corner
    drawCorner(
      Offset(margin, margin),
      Offset(margin + cornerLength, margin),
      Offset(margin, margin + cornerLength),
    );

    // Top-right corner
    drawCorner(
      Offset(size.width - margin, margin),
      Offset(size.width - margin - cornerLength, margin),
      Offset(size.width - margin, margin + cornerLength),
    );

    // Bottom-left corner
    drawCorner(
      Offset(margin, size.height - margin),
      Offset(margin + cornerLength, size.height - margin),
      Offset(margin, size.height - margin - cornerLength),
    );

    // Bottom-right corner
    drawCorner(
      Offset(size.width - margin, size.height - margin),
      Offset(size.width - margin - cornerLength, size.height - margin),
      Offset(size.width - margin, size.height - margin - cornerLength),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

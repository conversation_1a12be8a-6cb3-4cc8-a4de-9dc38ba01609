import 'package:flutter/material.dart';
import 'dart:typed_data';
import '../../../../core/constants/app_constants.dart';

class ImageEditorWidget extends StatefulWidget {
  final Uint8List imageBytes;
  final List<Offset> corners;
  final Function(List<Offset>) onCornersChanged;

  const ImageEditorWidget({
    super.key,
    required this.imageBytes,
    required this.corners,
    required this.onCornersChanged,
  });

  @override
  State<ImageEditorWidget> createState() => _ImageEditorWidgetState();
}

class _ImageEditorWidgetState extends State<ImageEditorWidget> {
  late List<Offset> _corners;
  Size? _imageSize;
  Size? _displaySize;

  @override
  void initState() {
    super.initState();
    _corners = List.from(widget.corners);
  }

  @override
  void didUpdateWidget(ImageEditorWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.corners != widget.corners) {
      _corners = List.from(widget.corners);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: Colors.white, width: 2),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius - 2),
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Stack(
              children: [
                // Image
                Image.memory(
                  widget.imageBytes,
                  fit: BoxFit.contain,
                  width: constraints.maxWidth,
                  height: constraints.maxHeight,
                  frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
                    if (frame != null) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _calculateImageSize(constraints);
                      });
                    }
                    return child;
                  },
                ),
                
                // Corner handles overlay
                if (_imageSize != null && _displaySize != null)
                  Positioned.fill(
                    child: CustomPaint(
                      painter: CornerHandlesPainter(
                        corners: _getScaledCorners(),
                        imageSize: _displaySize!,
                      ),
                    ),
                  ),
                
                // Gesture detector for corner dragging
                if (_imageSize != null && _displaySize != null)
                  Positioned.fill(
                    child: GestureDetector(
                      onPanStart: _onPanStart,
                      onPanUpdate: _onPanUpdate,
                      onPanEnd: _onPanEnd,
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _calculateImageSize(BoxConstraints constraints) {
    // This is a simplified calculation
    // In a real implementation, you'd need to calculate the actual displayed image size
    setState(() {
      _displaySize = Size(constraints.maxWidth, constraints.maxHeight);
      // For now, assume the image fills the available space
      // This should be calculated based on the actual image aspect ratio
    });
  }

  List<Offset> _getScaledCorners() {
    if (_imageSize == null || _displaySize == null) return _corners;
    
    // Scale corners from image coordinates to display coordinates
    // This is a simplified version - in reality, you'd need to account for
    // the actual image scaling and positioning within the display area
    return _corners.map((corner) {
      return Offset(
        corner.dx * _displaySize!.width / _imageSize!.width,
        corner.dy * _displaySize!.height / _imageSize!.height,
      );
    }).toList();
  }

  int? _draggedCornerIndex;

  void _onPanStart(DragStartDetails details) {
    final scaledCorners = _getScaledCorners();
    const touchRadius = 30.0;
    
    for (int i = 0; i < scaledCorners.length; i++) {
      final distance = (details.localPosition - scaledCorners[i]).distance;
      if (distance <= touchRadius) {
        setState(() {
          _draggedCornerIndex = i;
        });
        break;
      }
    }
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (_draggedCornerIndex != null && _imageSize != null && _displaySize != null) {
      // Convert display coordinates back to image coordinates
      final imageX = details.localPosition.dx * _imageSize!.width / _displaySize!.width;
      final imageY = details.localPosition.dy * _imageSize!.height / _displaySize!.height;
      
      setState(() {
        _corners[_draggedCornerIndex!] = Offset(
          imageX.clamp(0, _imageSize!.width),
          imageY.clamp(0, _imageSize!.height),
        );
      });
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (_draggedCornerIndex != null) {
      widget.onCornersChanged(_corners);
      setState(() {
        _draggedCornerIndex = null;
      });
    }
  }
}

class CornerHandlesPainter extends CustomPainter {
  final List<Offset> corners;
  final Size imageSize;

  CornerHandlesPainter({
    required this.corners,
    required this.imageSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final linePaint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final handlePaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.fill;

    final handleBorderPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Draw lines connecting corners
    if (corners.length >= 4) {
      final path = Path();
      path.moveTo(corners[0].dx, corners[0].dy);
      for (int i = 1; i < corners.length; i++) {
        path.lineTo(corners[i].dx, corners[i].dy);
      }
      path.close();
      canvas.drawPath(path, linePaint);
    }

    // Draw corner handles
    for (final corner in corners) {
      canvas.drawCircle(corner, 12, handlePaint);
      canvas.drawCircle(corner, 12, handleBorderPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

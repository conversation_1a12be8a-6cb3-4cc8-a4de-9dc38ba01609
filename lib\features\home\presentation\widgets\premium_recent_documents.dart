import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumRecentDocuments extends StatefulWidget {
  const PremiumRecentDocuments({super.key});

  @override
  State<PremiumRecentDocuments> createState() => _PremiumRecentDocumentsState();
}

class _PremiumRecentDocumentsState extends State<PremiumRecentDocuments>
    with TickerProviderStateMixin {
  late AnimationController _listController;
  late Animation<double> _listAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimation();
  }

  void _initializeAnimations() {
    _listController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _listAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _listController,
        curve: AppTheme.curveEmphasized,
      ),
    );
  }

  void _startAnimation() {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _listController.forward();
      }
    });
  }

  @override
  void dispose() {
    _listController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Documents',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pushNamed('/file-manager'),
              child: Text(
                'View All',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.brandPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacing16),
        AnimatedBuilder(
          animation: _listAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, (1 - _listAnimation.value) * 50),
              child: Opacity(
                opacity: _listAnimation.value,
                child: _buildDocumentsList(),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildDocumentsList() {
    final documents = _getMockDocuments();
    
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: documents.length,
      separatorBuilder: (context, index) => const SizedBox(height: AppTheme.spacing12),
      itemBuilder: (context, index) {
        final document = documents[index];
        return _buildDocumentCard(document, index);
      },
    );
  }

  Widget _buildDocumentCard(DocumentItem document, int index) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // Open document
      },
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacing16),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(AppTheme.radiusXL),
          border: Border.all(
            color: AppTheme.outlineColor,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.textPrimaryColor.withValues(alpha: 0.03),
              blurRadius: AppTheme.shadowBlurMD,
              offset: const Offset(0, AppTheme.spacing4),
            ),
          ],
        ),
        child: Row(
          children: [
            // Document Thumbnail
            Container(
              width: AppTheme.spacing56,
              height: AppTheme.spacing56,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: document.gradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(AppTheme.radiusMD),
                boxShadow: [
                  BoxShadow(
                    color: document.gradient.first.withValues(alpha: 0.3),
                    blurRadius: AppTheme.shadowBlurSM,
                    offset: const Offset(0, AppTheme.spacing2),
                  ),
                ],
              ),
              child: Icon(
                document.icon,
                color: AppTheme.textOnPrimaryColor,
                size: AppTheme.spacing24,
              ),
            ),
            
            const SizedBox(width: AppTheme.spacing16),
            
            // Document Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    document.name,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: AppTheme.spacing4),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacing8,
                          vertical: AppTheme.spacing2,
                        ),
                        decoration: BoxDecoration(
                          color: document.gradient.first.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                        ),
                        child: Text(
                          document.type,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: document.gradient.first,
                            fontWeight: FontWeight.w500,
                            fontSize: 10,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacing8),
                      Text(
                        document.size,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      Text(
                        ' • ',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textTertiaryColor,
                        ),
                      ),
                      Text(
                        document.date,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textTertiaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Action Button
            Container(
              width: AppTheme.spacing32,
              height: AppTheme.spacing32,
              decoration: BoxDecoration(
                color: AppTheme.backgroundSecondary,
                borderRadius: BorderRadius.circular(AppTheme.radiusSM),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                  onTap: () {
                    // Show document options
                  },
                  child: Icon(
                    Icons.more_vert_rounded,
                    color: AppTheme.textSecondaryColor,
                    size: AppTheme.spacing16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<DocumentItem> _getMockDocuments() {
    return [
      DocumentItem(
        name: 'Invoice_2024_001.pdf',
        type: 'PDF',
        size: '2.4 MB',
        date: '2 hours ago',
        icon: Icons.picture_as_pdf_outlined,
        gradient: [AppTheme.errorColor, AppTheme.errorLightColor],
      ),
      DocumentItem(
        name: 'Contract_Draft.pdf',
        type: 'PDF',
        size: '1.8 MB',
        date: 'Yesterday',
        icon: Icons.picture_as_pdf_outlined,
        gradient: [AppTheme.errorColor, AppTheme.errorLightColor],
      ),
      DocumentItem(
        name: 'Receipt_Grocery.jpg',
        type: 'IMAGE',
        size: '856 KB',
        date: '3 days ago',
        icon: Icons.image_outlined,
        gradient: [AppTheme.secondaryColor, AppTheme.secondaryLightColor],
      ),
      DocumentItem(
        name: 'Business_Card.jpg',
        type: 'IMAGE',
        size: '1.2 MB',
        date: '1 week ago',
        icon: Icons.image_outlined,
        gradient: [AppTheme.secondaryColor, AppTheme.secondaryLightColor],
      ),
    ];
  }
}

class DocumentItem {
  final String name;
  final String type;
  final String size;
  final String date;
  final IconData icon;
  final List<Color> gradient;

  DocumentItem({
    required this.name,
    required this.type,
    required this.size,
    required this.date,
    required this.icon,
    required this.gradient,
  });
}

import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumStorageIndicator extends StatefulWidget {
  const PremiumStorageIndicator({super.key});

  @override
  State<PremiumStorageIndicator> createState() => _PremiumStorageIndicatorState();
}

class _PremiumStorageIndicatorState extends State<PremiumStorageIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimation();
    _startAnimation();
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 0.68, // 68% storage used
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppTheme.curveEmphasized,
    ));
  }

  void _startAnimation() {
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing20,
        vertical: AppTheme.spacing12,
      ),
      padding: const EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusXL),
        border: Border.all(
          color: AppTheme.outlineColor,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
            blurRadius: AppTheme.shadowBlurMD,
            offset: const Offset(0, AppTheme.spacing4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppTheme.spacing8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: AppTheme.secondaryGradient,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(AppTheme.radiusMD),
                    ),
                    child: Icon(
                      Icons.cloud_outlined,
                      color: AppTheme.textOnPrimaryColor,
                      size: AppTheme.spacing16,
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacing12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Storage',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      Text(
                        '2.4 GB of 3.5 GB used',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing8,
                  vertical: AppTheme.spacing4,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.warningColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                ),
                child: Text(
                  '68%',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.warningColor,
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppTheme.spacing16),
          
          // Progress Bar
          AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return Column(
                children: [
                  Container(
                    height: AppTheme.spacing6,
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundSecondary,
                      borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                    ),
                    child: Stack(
                      children: [
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: AppTheme.backgroundSecondary,
                            borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                          ),
                        ),
                        FractionallySizedBox(
                          widthFactor: _progressAnimation.value,
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: _getProgressGradient(_progressAnimation.value),
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: AppTheme.spacing12),
                  
                  // Storage Breakdown
                  Row(
                    children: [
                      _buildStorageItem(
                        color: AppTheme.brandPrimary,
                        label: 'Documents',
                        size: '1.8 GB',
                      ),
                      const SizedBox(width: AppTheme.spacing16),
                      _buildStorageItem(
                        color: AppTheme.secondaryColor,
                        label: 'Images',
                        size: '0.6 GB',
                      ),
                      const Spacer(),
                      Text(
                        '1.1 GB free',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textTertiaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  List<Color> _getProgressGradient(double progress) {
    if (progress < 0.5) {
      return [AppTheme.successColor, AppTheme.successLightColor];
    } else if (progress < 0.8) {
      return [AppTheme.warningColor, AppTheme.warningLightColor];
    } else {
      return [AppTheme.errorColor, AppTheme.errorLightColor];
    }
  }

  Widget _buildStorageItem({
    required Color color,
    required String label,
    required String size,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: AppTheme.spacing8,
          height: AppTheme.spacing8,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(AppTheme.radiusRound),
          ),
        ),
        const SizedBox(width: AppTheme.spacing6),
        Text(
          '$label $size',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppTheme.textSecondaryColor,
            fontSize: 11,
          ),
        ),
      ],
    );
  }
}

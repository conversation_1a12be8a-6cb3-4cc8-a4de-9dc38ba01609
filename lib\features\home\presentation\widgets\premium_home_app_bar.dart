import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumHomeAppBar extends StatelessWidget {
  const PremiumHomeAppBar({super.key});

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 17) {
      return 'Good Afternoon';
    } else {
      return 'Good Evening';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        border: Border(
          bottom: BorderSide(
            color: AppTheme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Top Row - Logo and Profile
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Logo and App Name
              Row(
                children: [
                  Container(
                    width: AppTheme.spacing40,
                    height: AppTheme.spacing40,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: AppTheme.primaryGradient,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(AppTheme.radiusMD),
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.brandPrimary.withValues(alpha: 0.3),
                          blurRadius: AppTheme.shadowBlurMD,
                          offset: const Offset(0, AppTheme.spacing4),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.document_scanner_rounded,
                      color: AppTheme.textOnPrimaryColor,
                      size: AppTheme.spacing20,
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacing12),
                  Text(
                    'LensDoc',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
              
              // Profile and Notifications
              Row(
                children: [
                  _buildActionButton(
                    icon: Icons.notifications_outlined,
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      // Handle notifications
                    },
                    hasNotification: true,
                  ),
                  const SizedBox(width: AppTheme.spacing12),
                  _buildActionButton(
                    icon: Icons.person_outline_rounded,
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      // Handle profile
                    },
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: AppTheme.spacing20),
          
          // Greeting and Welcome Message
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getGreeting(),
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacing4),
                    Text(
                      'Ready to scan some documents?',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    bool hasNotification = false,
  }) {
    return Stack(
      children: [
        Container(
          width: AppTheme.spacing40,
          height: AppTheme.spacing40,
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusMD),
            border: Border.all(
              color: AppTheme.outlineColor,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
                blurRadius: AppTheme.shadowBlurSM,
                offset: const Offset(0, AppTheme.spacing2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(AppTheme.radiusMD),
              onTap: onPressed,
              child: Icon(
                icon,
                color: AppTheme.textSecondaryColor,
                size: AppTheme.spacing20,
              ),
            ),
          ),
        ),
        
        if (hasNotification)
          Positioned(
            top: AppTheme.spacing6,
            right: AppTheme.spacing6,
            child: Container(
              width: AppTheme.spacing8,
              height: AppTheme.spacing8,
              decoration: BoxDecoration(
                color: AppTheme.errorColor,
                borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                border: Border.all(
                  color: AppTheme.backgroundColor,
                  width: 1,
                ),
              ),
            ),
          ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumQuickActions extends StatefulWidget {
  const PremiumQuickActions({super.key});

  @override
  State<PremiumQuickActions> createState() => _PremiumQuickActionsState();
}

class _PremiumQuickActionsState extends State<PremiumQuickActions>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startStaggeredAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      4,
      (index) => AnimationController(
        duration: AppTheme.animationNormal,
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: controller,
          curve: AppTheme.curveEmphasized,
        ),
      );
    }).toList();
  }

  void _startStaggeredAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: 150 * i), () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () {
                // Show all actions
              },
              child: Text(
                'See All',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.brandPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacing16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: AppTheme.spacing16,
          mainAxisSpacing: AppTheme.spacing16,
          childAspectRatio: 1.2,
          children: [
            AnimatedBuilder(
              animation: _animations[0],
              builder: (context, child) {
                return Transform.scale(
                  scale: _animations[0].value,
                  child: Opacity(
                    opacity: _animations[0].value,
                    child: _buildQuickActionCard(
                      title: 'My Files',
                      subtitle: '24 documents',
                      icon: Icons.folder_outlined,
                      gradient: AppTheme.primaryGradient,
                      onTap: () => Navigator.of(context).pushNamed('/file-manager'),
                    ),
                  ),
                );
              },
            ),
            AnimatedBuilder(
              animation: _animations[1],
              builder: (context, child) {
                return Transform.scale(
                  scale: _animations[1].value,
                  child: Opacity(
                    opacity: _animations[1].value,
                    child: _buildQuickActionCard(
                      title: 'OCR Text',
                      subtitle: 'Extract text',
                      icon: Icons.text_fields_outlined,
                      gradient: AppTheme.secondaryGradient,
                      onTap: () {
                        // Navigate to OCR
                      },
                    ),
                  ),
                );
              },
            ),
            AnimatedBuilder(
              animation: _animations[2],
              builder: (context, child) {
                return Transform.scale(
                  scale: _animations[2].value,
                  child: Opacity(
                    opacity: _animations[2].value,
                    child: _buildQuickActionCard(
                      title: 'Share',
                      subtitle: 'Send documents',
                      icon: Icons.share_outlined,
                      gradient: AppTheme.accentGradient,
                      onTap: () {
                        // Share functionality
                      },
                    ),
                  ),
                );
              },
            ),
            AnimatedBuilder(
              animation: _animations[3],
              builder: (context, child) {
                return Transform.scale(
                  scale: _animations[3].value,
                  child: Opacity(
                    opacity: _animations[3].value,
                    child: _buildQuickActionCard(
                      title: 'Settings',
                      subtitle: 'Preferences',
                      icon: Icons.settings_outlined,
                      gradient: [
                        AppTheme.textSecondaryColor,
                        AppTheme.textTertiaryColor,
                      ],
                      onTap: () => Navigator.of(context).pushNamed('/settings'),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Color> gradient,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(AppTheme.radius2XL),
          border: Border.all(
            color: AppTheme.outlineColor,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
              blurRadius: AppTheme.shadowBlurLG,
              offset: const Offset(0, AppTheme.spacing8),
            ),
            BoxShadow(
              color: AppTheme.textPrimaryColor.withValues(alpha: 0.02),
              blurRadius: AppTheme.shadowBlur2XL,
              offset: const Offset(0, AppTheme.spacing16),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacing20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: AppTheme.spacing48,
                height: AppTheme.spacing48,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: gradient,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                  boxShadow: [
                    BoxShadow(
                      color: gradient.first.withValues(alpha: 0.3),
                      blurRadius: AppTheme.shadowBlurMD,
                      offset: const Offset(0, AppTheme.spacing4),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: AppTheme.textOnPrimaryColor,
                  size: AppTheme.spacing24,
                ),
              ),
              const Spacer(),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(height: AppTheme.spacing4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              const SizedBox(height: AppTheme.spacing8),
              Container(
                padding: const EdgeInsets.all(AppTheme.spacing6),
                decoration: BoxDecoration(
                  color: gradient.first.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                ),
                child: Icon(
                  Icons.arrow_forward_rounded,
                  color: gradient.first,
                  size: AppTheme.spacing16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

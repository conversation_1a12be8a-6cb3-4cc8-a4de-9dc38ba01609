import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';

class EnhancedFAB extends StatefulWidget {
  final VoidCallback onPressed;
  final IconData icon;
  final String? label;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool isExtended;
  final bool isLoading;
  final double? elevation;
  final EdgeInsetsGeometry? padding;

  const EnhancedFAB({
    super.key,
    required this.onPressed,
    required this.icon,
    this.label,
    this.backgroundColor,
    this.foregroundColor,
    this.isExtended = false,
    this.isLoading = false,
    this.elevation,
    this.padding,
  });

  @override
  State<EnhancedFAB> createState() => _EnhancedFABState();
}

class _EnhancedFABState extends State<EnhancedFAB>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _pulseController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    
    _scaleController = AnimationController(
      duration: AppTheme.animationFast,
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: AppTheme.animationSlower,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: AppTheme.curveStandard,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: AppTheme.curveStandard,
    ));

    if (widget.isLoading) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(EnhancedFAB oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _pulseController.repeat(reverse: true);
      } else {
        _pulseController.stop();
        _pulseController.reset();
      }
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _scaleController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _scaleController.reverse();
    if (!widget.isLoading) {
      widget.onPressed();
    }
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
    _scaleController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor = widget.backgroundColor ?? AppTheme.primaryColor;
    final foregroundColor = widget.foregroundColor ?? AppTheme.textOnPrimaryColor;

    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _pulseAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value * (widget.isLoading ? _pulseAnimation.value : 1.0),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(
                widget.isExtended ? AppTheme.radius2XL : AppTheme.radiusRound,
              ),
              boxShadow: [
                BoxShadow(
                  color: backgroundColor.withValues(alpha: 0.3),
                  blurRadius: widget.elevation ?? AppTheme.spacing16,
                  offset: const Offset(0, AppTheme.spacing8),
                ),
                BoxShadow(
                  color: backgroundColor.withValues(alpha: 0.1),
                  blurRadius: (widget.elevation ?? AppTheme.spacing16) * 2,
                  offset: const Offset(0, AppTheme.spacing12),
                ),
                if (_isPressed)
                  BoxShadow(
                    color: backgroundColor.withValues(alpha: 0.4),
                    blurRadius: AppTheme.spacing24,
                    offset: const Offset(0, AppTheme.spacing16),
                  ),
              ],
            ),
            child: Material(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(
                widget.isExtended ? AppTheme.radius2XL : AppTheme.radiusRound,
              ),
              child: InkWell(
                borderRadius: BorderRadius.circular(
                  widget.isExtended ? AppTheme.radius2XL : AppTheme.radiusRound,
                ),
                onTapDown: _onTapDown,
                onTapUp: _onTapUp,
                onTapCancel: _onTapCancel,
                child: Container(
                  padding: widget.padding ?? 
                    (widget.isExtended 
                      ? const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacing24,
                          vertical: AppTheme.spacing16,
                        )
                      : const EdgeInsets.all(AppTheme.spacing16)),
                  child: widget.isLoading
                      ? _buildLoadingContent(foregroundColor)
                      : _buildNormalContent(foregroundColor),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingContent(Color foregroundColor) {
    if (widget.isExtended && widget.label != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: AppTheme.spacing20,
            height: AppTheme.spacing20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(foregroundColor),
            ),
          ),
          const SizedBox(width: AppTheme.spacing12),
          Text(
            'Loading...',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: foregroundColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }

    return SizedBox(
      width: AppTheme.spacing24,
      height: AppTheme.spacing24,
      child: CircularProgressIndicator(
        strokeWidth: 2.5,
        valueColor: AlwaysStoppedAnimation<Color>(foregroundColor),
      ),
    );
  }

  Widget _buildNormalContent(Color foregroundColor) {
    if (widget.isExtended && widget.label != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.icon,
            color: foregroundColor,
            size: AppTheme.spacing24,
          ),
          const SizedBox(width: AppTheme.spacing12),
          Text(
            widget.label!,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: foregroundColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }

    return Icon(
      widget.icon,
      color: foregroundColor,
      size: AppTheme.spacing28,
    );
  }
}

class EnhancedSpeedDial extends StatefulWidget {
  final List<SpeedDialChild> children;
  final IconData icon;
  final IconData? activeIcon;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final String? tooltip;

  const EnhancedSpeedDial({
    super.key,
    required this.children,
    required this.icon,
    this.activeIcon,
    this.backgroundColor,
    this.foregroundColor,
    this.tooltip,
  });

  @override
  State<EnhancedSpeedDial> createState() => _EnhancedSpeedDialState();
}

class _EnhancedSpeedDialState extends State<EnhancedSpeedDial>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _expandAnimation;
  bool _isOpen = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _controller,
      curve: AppTheme.curveEmphasized,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggle() {
    setState(() {
      _isOpen = !_isOpen;
    });
    if (_isOpen) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        ..._buildSpeedDialChildren(),
        const SizedBox(height: AppTheme.spacing16),
        EnhancedFAB(
          onPressed: _toggle,
          icon: _isOpen ? (widget.activeIcon ?? Icons.close) : widget.icon,
          backgroundColor: widget.backgroundColor,
          foregroundColor: widget.foregroundColor,
        ),
      ],
    );
  }

  List<Widget> _buildSpeedDialChildren() {
    return widget.children.asMap().entries.map((entry) {
      final index = entry.key;
      final child = entry.value;
      
      return AnimatedBuilder(
        animation: _expandAnimation,
        builder: (context, _) {
          final animationValue = Curves.easeOut.transform(
            (_expandAnimation.value - (index * 0.1)).clamp(0.0, 1.0),
          );
          
          return Transform.translate(
            offset: Offset(0, (1 - animationValue) * 50),
            child: Opacity(
              opacity: animationValue,
              child: Padding(
                padding: const EdgeInsets.only(bottom: AppTheme.spacing12),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (child.label != null) ...[
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacing12,
                          vertical: AppTheme.spacing8,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.surfaceColor,
                          borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.textPrimaryColor.withValues(alpha: 0.1),
                              blurRadius: AppTheme.spacing8,
                              offset: const Offset(0, AppTheme.spacing2),
                            ),
                          ],
                        ),
                        child: Text(
                          child.label!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacing12),
                    ],
                    EnhancedFAB(
                      onPressed: () {
                        child.onPressed();
                        _toggle();
                      },
                      icon: child.icon,
                      backgroundColor: child.backgroundColor,
                      foregroundColor: child.foregroundColor,
                      elevation: AppTheme.elevation4,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    }).toList().reversed.toList();
  }
}

class SpeedDialChild {
  final IconData icon;
  final VoidCallback onPressed;
  final String? label;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const SpeedDialChild({
    required this.icon,
    required this.onPressed,
    this.label,
    this.backgroundColor,
    this.foregroundColor,
  });
}

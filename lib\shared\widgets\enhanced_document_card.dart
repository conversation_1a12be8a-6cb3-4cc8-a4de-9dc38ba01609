import 'package:flutter/material.dart';
import 'dart:io';
import '../../core/theme/app_theme.dart';

class EnhancedDocumentCard extends StatefulWidget {
  final File file;
  final String fileType;
  final VoidCallback onTap;
  final VoidCallback onDelete;
  final VoidCallback onShare;
  final bool isGridView;

  const EnhancedDocumentCard({
    super.key,
    required this.file,
    required this.fileType,
    required this.onTap,
    required this.onDelete,
    required this.onShare,
    this.isGridView = true,
  });

  @override
  State<EnhancedDocumentCard> createState() => _EnhancedDocumentCardState();
}

class _EnhancedDocumentCardState extends State<EnhancedDocumentCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppTheme.animationFast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppTheme.curveStandard,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _animationController.reverse();
    widget.onTap();
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: widget.isGridView ? _buildGridCard() : _buildListCard(),
        );
      },
    );
  }

  Widget _buildGridCard() {
    final fileName = widget.file.path.split('/').last;
    final fileSize = _getFileSize();
    final lastModified = _getLastModified();

    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(AppTheme.radiusXL),
          border: Border.all(
            color: _isPressed
                ? AppTheme.primaryColor.withValues(alpha: 0.3)
                : AppTheme.outlineColor,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
              blurRadius: AppTheme.spacing12,
              offset: const Offset(0, AppTheme.spacing6),
            ),
            if (_isPressed)
              BoxShadow(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                blurRadius: AppTheme.spacing20,
                offset: const Offset(0, AppTheme.spacing8),
              ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // File preview/icon
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      _getFileTypeColor().withValues(alpha: 0.1),
                      _getFileTypeColor().withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppTheme.radiusXL),
                    topRight: Radius.circular(AppTheme.radiusXL),
                  ),
                ),
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(AppTheme.spacing16),
                    decoration: BoxDecoration(
                      color: _getFileTypeColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                    ),
                    child: Icon(
                      _getFileTypeIcon(),
                      size: AppTheme.spacing40,
                      color: _getFileTypeColor(),
                    ),
                  ),
                ),
              ),
            ),
            
            // File info
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacing16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fileName,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimaryColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppTheme.spacing4),
                    Text(
                      fileSize,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacing2),
                    Text(
                      lastModified,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textTertiaryColor,
                        fontSize: 11,
                      ),
                    ),
                    const Spacer(),
                    
                    // Action buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        _buildActionButton(
                          icon: Icons.share_outlined,
                          onPressed: widget.onShare,
                          color: AppTheme.secondaryColor,
                        ),
                        const SizedBox(width: AppTheme.spacing8),
                        _buildActionButton(
                          icon: Icons.delete_outline,
                          onPressed: widget.onDelete,
                          color: AppTheme.errorColor,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListCard() {
    final fileName = widget.file.path.split('/').last;
    final fileSize = _getFileSize();
    final lastModified = _getLastModified();

    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: Container(
        margin: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacing16,
          vertical: AppTheme.spacing6,
        ),
        padding: const EdgeInsets.all(AppTheme.spacing16),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(AppTheme.radiusLG),
          border: Border.all(
            color: _isPressed
                ? AppTheme.primaryColor.withValues(alpha: 0.3)
                : AppTheme.outlineColor,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.textPrimaryColor.withValues(alpha: 0.03),
              blurRadius: AppTheme.spacing8,
              offset: const Offset(0, AppTheme.spacing4),
            ),
          ],
        ),
        child: Row(
          children: [
            // File icon
            Container(
              width: AppTheme.spacing48,
              height: AppTheme.spacing48,
              decoration: BoxDecoration(
                color: _getFileTypeColor().withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusMD),
              ),
              child: Icon(
                _getFileTypeIcon(),
                color: _getFileTypeColor(),
                size: AppTheme.spacing24,
              ),
            ),
            
            const SizedBox(width: AppTheme.spacing16),
            
            // File info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    fileName,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: AppTheme.spacing4),
                  Row(
                    children: [
                      Text(
                        fileSize,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      Text(
                        ' • ',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textTertiaryColor,
                        ),
                      ),
                      Text(
                        lastModified,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textTertiaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Action buttons
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildActionButton(
                  icon: Icons.share_outlined,
                  onPressed: widget.onShare,
                  color: AppTheme.secondaryColor,
                ),
                const SizedBox(width: AppTheme.spacing8),
                _buildActionButton(
                  icon: Icons.delete_outline,
                  onPressed: widget.onDelete,
                  color: AppTheme.errorColor,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return Container(
      width: AppTheme.spacing32,
      height: AppTheme.spacing32,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSM),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppTheme.radiusSM),
          onTap: onPressed,
          child: Icon(
            icon,
            size: AppTheme.spacing16,
            color: color,
          ),
        ),
      ),
    );
  }

  Color _getFileTypeColor() {
    switch (widget.fileType.toLowerCase()) {
      case 'pdf':
        return AppTheme.errorColor;
      case 'image':
        return AppTheme.secondaryColor;
      default:
        return AppTheme.textSecondaryColor;
    }
  }

  IconData _getFileTypeIcon() {
    switch (widget.fileType.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf_outlined;
      case 'image':
        return Icons.image_outlined;
      default:
        return Icons.insert_drive_file_outlined;
    }
  }

  String _getFileSize() {
    try {
      final bytes = widget.file.lengthSync();
      if (bytes < 1024) return '${bytes}B';
      if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    } catch (e) {
      return 'Unknown';
    }
  }

  String _getLastModified() {
    try {
      final modified = widget.file.lastModifiedSync();
      final now = DateTime.now();
      final difference = now.difference(modified);
      
      if (difference.inDays > 0) {
        return '${difference.inDays}d ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours}h ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}m ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return 'Unknown';
    }
  }
}

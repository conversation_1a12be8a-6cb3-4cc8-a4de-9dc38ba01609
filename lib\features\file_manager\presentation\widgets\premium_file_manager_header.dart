import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumFileManagerHeader extends StatelessWidget {
  final bool isSelectionMode;
  final int selectedCount;
  final VoidCallback onToggleSelection;
  final VoidCallback onToggleView;
  final bool isGridView;

  const PremiumFileManagerHeader({
    super.key,
    required this.isSelectionMode,
    required this.selectedCount,
    required this.onToggleSelection,
    required this.onToggleView,
    required this.isGridView,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        border: Border(
          bottom: BorderSide(
            color: AppTheme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: isSelectionMode ? _buildSelectionHeader(context) : _buildNormalHeader(context),
    );
  }

  Widget _buildNormalHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'My Documents',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacing4),
            Text(
              'Manage your scanned documents',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
        Row(
          children: [
            _buildHeaderButton(
              icon: isGridView ? Icons.view_list_rounded : Icons.grid_view_rounded,
              onPressed: () {
                HapticFeedback.lightImpact();
                onToggleView();
              },
              tooltip: isGridView ? 'List View' : 'Grid View',
            ),
            const SizedBox(width: AppTheme.spacing12),
            _buildHeaderButton(
              icon: Icons.select_all_rounded,
              onPressed: () {
                HapticFeedback.lightImpact();
                onToggleSelection();
              },
              tooltip: 'Select Files',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSelectionHeader(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            onToggleSelection();
          },
          child: Container(
            padding: const EdgeInsets.all(AppTheme.spacing8),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusMD),
              border: Border.all(
                color: AppTheme.outlineColor,
                width: 1,
              ),
            ),
            child: Icon(
              Icons.close_rounded,
              color: AppTheme.textSecondaryColor,
              size: AppTheme.spacing20,
            ),
          ),
        ),
        
        const SizedBox(width: AppTheme.spacing16),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '$selectedCount Selected',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Text(
                'Tap files to select or deselect',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
        
        if (selectedCount > 0) ...[
          _buildSelectionAction(
            icon: Icons.share_rounded,
            onPressed: () {
              HapticFeedback.lightImpact();
              // Handle share
            },
          ),
          const SizedBox(width: AppTheme.spacing8),
          _buildSelectionAction(
            icon: Icons.delete_outline_rounded,
            onPressed: () {
              HapticFeedback.lightImpact();
              // Handle delete
            },
            isDestructive: true,
          ),
        ],
      ],
    );
  }

  Widget _buildHeaderButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        width: AppTheme.spacing40,
        height: AppTheme.spacing40,
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(AppTheme.radiusMD),
          border: Border.all(
            color: AppTheme.outlineColor,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
              blurRadius: AppTheme.shadowBlurSM,
              offset: const Offset(0, AppTheme.spacing2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(AppTheme.radiusMD),
            onTap: onPressed,
            child: Icon(
              icon,
              color: AppTheme.textSecondaryColor,
              size: AppTheme.spacing20,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectionAction({
    required IconData icon,
    required VoidCallback onPressed,
    bool isDestructive = false,
  }) {
    return Container(
      width: AppTheme.spacing40,
      height: AppTheme.spacing40,
      decoration: BoxDecoration(
        color: isDestructive 
            ? AppTheme.errorColor.withValues(alpha: 0.1)
            : AppTheme.brandPrimary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusMD),
        border: Border.all(
          color: isDestructive 
              ? AppTheme.errorColor.withValues(alpha: 0.3)
              : AppTheme.brandPrimary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppTheme.radiusMD),
          onTap: onPressed,
          child: Icon(
            icon,
            color: isDestructive ? AppTheme.errorColor : AppTheme.brandPrimary,
            size: AppTheme.spacing20,
          ),
        ),
      ),
    );
  }
}

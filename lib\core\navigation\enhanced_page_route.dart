import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class EnhancedPageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  final RouteTransitionType transitionType;
  final Duration duration;
  final Curve curve;

  EnhancedPageRoute({
    required this.child,
    this.transitionType = RouteTransitionType.slideFromRight,
    this.duration = AppTheme.animationNormal,
    this.curve = AppTheme.curveEmphasized,
    super.settings,
  }) : super(
         pageBuilder: (context, animation, secondaryAnimation) => child,
         transitionDuration: duration,
         reverseTransitionDuration: duration,
       );

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    final curvedAnimation = CurvedAnimation(parent: animation, curve: curve);

    switch (transitionType) {
      case RouteTransitionType.slideFromRight:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );

      case RouteTransitionType.slideFromLeft:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(-1.0, 0.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );

      case RouteTransitionType.slideFromBottom:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, 1.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );

      case RouteTransitionType.slideFromTop:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, -1.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );

      case RouteTransitionType.fade:
        return FadeTransition(opacity: curvedAnimation, child: child);

      case RouteTransitionType.scale:
        return ScaleTransition(
          scale: Tween<double>(begin: 0.8, end: 1.0).animate(curvedAnimation),
          child: FadeTransition(opacity: curvedAnimation, child: child),
        );

      case RouteTransitionType.rotation:
        return RotationTransition(
          turns: Tween<double>(begin: 0.8, end: 1.0).animate(curvedAnimation),
          child: FadeTransition(opacity: curvedAnimation, child: child),
        );

      case RouteTransitionType.slideAndFade:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.3, 0.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: FadeTransition(opacity: curvedAnimation, child: child),
        );

      case RouteTransitionType.scaleAndSlide:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, 0.1),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: ScaleTransition(
            scale: Tween<double>(begin: 0.9, end: 1.0).animate(curvedAnimation),
            child: child,
          ),
        );
    }
  }
}

enum RouteTransitionType {
  slideFromRight,
  slideFromLeft,
  slideFromBottom,
  slideFromTop,
  fade,
  scale,
  rotation,
  slideAndFade,
  scaleAndSlide,
}

class EnhancedNavigator {
  static Future<T?> push<T extends Object?>(
    BuildContext context,
    Widget page, {
    RouteTransitionType transitionType = RouteTransitionType.slideFromRight,
    Duration? duration,
    Curve? curve,
  }) {
    return Navigator.of(context).push<T>(
      EnhancedPageRoute<T>(
        child: page,
        transitionType: transitionType,
        duration: duration ?? AppTheme.animationNormal,
        curve: curve ?? AppTheme.curveEmphasized,
      ),
    );
  }

  static Future<T?> pushReplacement<T extends Object?, TO extends Object?>(
    BuildContext context,
    Widget page, {
    RouteTransitionType transitionType = RouteTransitionType.slideFromRight,
    Duration? duration,
    Curve? curve,
    TO? result,
  }) {
    return Navigator.of(context).pushReplacement<T, TO>(
      EnhancedPageRoute<T>(
        child: page,
        transitionType: transitionType,
        duration: duration ?? AppTheme.animationNormal,
        curve: curve ?? AppTheme.curveEmphasized,
      ),
      result: result,
    );
  }

  static Future<T?> pushAndRemoveUntil<T extends Object?>(
    BuildContext context,
    Widget page,
    RoutePredicate predicate, {
    RouteTransitionType transitionType = RouteTransitionType.slideFromRight,
    Duration? duration,
    Curve? curve,
  }) {
    return Navigator.of(context).pushAndRemoveUntil<T>(
      EnhancedPageRoute<T>(
        child: page,
        transitionType: transitionType,
        duration: duration ?? AppTheme.animationNormal,
        curve: curve ?? AppTheme.curveEmphasized,
      ),
      predicate,
    );
  }

  static void pop<T extends Object?>(BuildContext context, [T? result]) {
    Navigator.of(context).pop<T>(result);
  }

  static void popUntil(BuildContext context, RoutePredicate predicate) {
    Navigator.of(context).popUntil(predicate);
  }

  static bool canPop(BuildContext context) {
    return Navigator.of(context).canPop();
  }
}

// Hero animation helper
class EnhancedHero extends StatelessWidget {
  final String tag;
  final Widget child;
  final Duration? flightDuration;

  const EnhancedHero({
    super.key,
    required this.tag,
    required this.child,
    this.flightDuration,
  });

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: tag,
      flightShuttleBuilder: (
        BuildContext flightContext,
        Animation<double> animation,
        HeroFlightDirection flightDirection,
        BuildContext fromHeroContext,
        BuildContext toHeroContext,
      ) {
        return AnimatedBuilder(
          animation: animation,
          builder: (context, child) {
            return Transform.scale(
              scale: 1.0 + (animation.value * 0.1),
              child: child,
            );
          },
          child: Material(
            color: Colors.transparent,
            child: toHeroContext.widget,
          ),
        );
      },
      child: child,
    );
  }
}

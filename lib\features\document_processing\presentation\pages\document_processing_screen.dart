import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:typed_data';
import '../../../../core/di/injection_container.dart';
import '../../../../core/constants/app_constants.dart';
import '../bloc/document_processing_bloc.dart';
import '../bloc/document_processing_event.dart';
import '../bloc/document_processing_state.dart';
import '../widgets/image_editor_widget.dart';
import '../widgets/processing_controls.dart';

class DocumentProcessingScreen extends StatelessWidget {
  final Uint8List imageBytes;

  const DocumentProcessingScreen({super.key, required this.imageBytes});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (context) =>
              sl<DocumentProcessingBloc>()
                ..add(ProcessImageEvent(imageBytes: imageBytes)),
      child: DocumentProcessingView(imageBytes: imageBytes),
    );
  }
}

class DocumentProcessingView extends StatelessWidget {
  final Uint8List imageBytes;

  const DocumentProcessingView({super.key, required this.imageBytes});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'Adjust Document',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          BlocBuilder<DocumentProcessingBloc, DocumentProcessingState>(
            builder: (context, state) {
              if (state is ImageCropped) {
                return IconButton(
                  icon: const Icon(Icons.check, color: Colors.white),
                  onPressed: () {
                    Navigator.of(context).pushNamed(
                      '/document-preview',
                      arguments: [state.croppedImage],
                    );
                  },
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: BlocConsumer<DocumentProcessingBloc, DocumentProcessingState>(
        listener: (context, state) {
          if (state is DocumentProcessingError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // Image Editor Area
              Expanded(child: _buildImageEditor(context, state)),

              // Processing Controls
              ProcessingControls(
                onCrop: () {
                  if (state is EdgeDetectionCompleted) {
                    context.read<DocumentProcessingBloc>().add(
                      CropImageEvent(
                        imageBytes: state.originalImage,
                        corners: state.detectedCorners,
                      ),
                    );
                  } else if (state is CornersUpdated) {
                    context.read<DocumentProcessingBloc>().add(
                      CropImageEvent(
                        imageBytes: state.originalImage,
                        corners: state.corners,
                      ),
                    );
                  }
                },
                onRetake: () {
                  Navigator.of(context).pop();
                },
                onEnhance: () {
                  if (state is ImageCropped) {
                    context.read<DocumentProcessingBloc>().add(
                      EnhanceImageEvent(imageBytes: state.croppedImage),
                    );
                  }
                },
                canCrop:
                    state is EdgeDetectionCompleted || state is CornersUpdated,
                canEnhance: state is ImageCropped,
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildImageEditor(
    BuildContext context,
    DocumentProcessingState state,
  ) {
    if (state is DocumentProcessingLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text('Processing image...', style: TextStyle(color: Colors.white)),
          ],
        ),
      );
    }

    if (state is EdgeDetectionCompleted) {
      return ImageEditorWidget(
        imageBytes: state.originalImage,
        corners: state.detectedCorners,
        onCornersChanged: (corners) {
          context.read<DocumentProcessingBloc>().add(
            UpdateCornersEvent(corners: corners),
          );
        },
      );
    }

    if (state is CornersUpdated) {
      return ImageEditorWidget(
        imageBytes: state.originalImage,
        corners: state.corners,
        onCornersChanged: (corners) {
          context.read<DocumentProcessingBloc>().add(
            UpdateCornersEvent(corners: corners),
          );
        },
      );
    }

    if (state is ImageCropped) {
      return Container(
        margin: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius - 2),
          child: Image.memory(state.croppedImage, fit: BoxFit.contain),
        ),
      );
    }

    if (state is DocumentProcessingError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 64),
            const SizedBox(height: 16),
            Text(
              'Processing Error',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.message,
              style: const TextStyle(color: Colors.white70, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context.read<DocumentProcessingBloc>().add(
                  ProcessImageEvent(imageBytes: imageBytes),
                );
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return const Center(
      child: Text('Initializing...', style: TextStyle(color: Colors.white)),
    );
  }
}

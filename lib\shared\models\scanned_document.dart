import 'dart:io';

class ScannedDocument {
  final String id;
  final String name;
  final List<String> imagePaths;
  final String? pdfPath;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int pageCount;

  ScannedDocument({
    required this.id,
    required this.name,
    required this.imagePaths,
    this.pdfPath,
    required this.createdAt,
    required this.updatedAt,
    required this.pageCount,
  });

  ScannedDocument copyWith({
    String? id,
    String? name,
    List<String>? imagePaths,
    String? pdfPath,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? pageCount,
  }) {
    return ScannedDocument(
      id: id ?? this.id,
      name: name ?? this.name,
      imagePaths: imagePaths ?? this.imagePaths,
      pdfPath: pdfPath ?? this.pdfPath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      pageCount: pageCount ?? this.pageCount,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'imagePaths': imagePaths,
      'pdfPath': pdfPath,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'pageCount': pageCount,
    };
  }

  factory ScannedDocument.fromJson(Map<String, dynamic> json) {
    return ScannedDocument(
      id: json['id'] as String,
      name: json['name'] as String,
      imagePaths: List<String>.from(json['imagePaths'] as List),
      pdfPath: json['pdfPath'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      pageCount: json['pageCount'] as int,
    );
  }

  /// Get the first image file if available
  File? get firstImage {
    if (imagePaths.isNotEmpty) {
      return File(imagePaths.first);
    }
    return null;
  }

  /// Get all image files
  List<File> get imageFiles {
    return imagePaths.map((path) => File(path)).toList();
  }

  /// Get PDF file if available
  File? get pdfFile {
    if (pdfPath != null) {
      return File(pdfPath!);
    }
    return null;
  }

  /// Check if document has PDF
  bool get hasPdf => pdfPath != null && pdfPath!.isNotEmpty;

  /// Get document size in MB
  double get sizeInMB {
    double totalSize = 0;
    
    // Add image files size
    for (final imagePath in imagePaths) {
      final file = File(imagePath);
      if (file.existsSync()) {
        totalSize += file.lengthSync();
      }
    }
    
    // Add PDF file size if exists
    if (hasPdf) {
      final pdfFile = File(pdfPath!);
      if (pdfFile.existsSync()) {
        totalSize += pdfFile.lengthSync();
      }
    }
    
    return totalSize / (1024 * 1024); // Convert to MB
  }

  @override
  String toString() {
    return 'ScannedDocument(id: $id, name: $name, pageCount: $pageCount, hasPdf: $hasPdf)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ScannedDocument && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

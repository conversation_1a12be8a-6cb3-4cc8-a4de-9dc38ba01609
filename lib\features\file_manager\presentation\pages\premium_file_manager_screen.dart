import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/widgets/enhanced_search_bar.dart';
import '../../../../shared/widgets/enhanced_document_card.dart';
import '../widgets/premium_file_manager_header.dart';
import '../widgets/premium_sort_filter_bar.dart';
import '../widgets/premium_storage_indicator.dart';

class PremiumFileManagerScreen extends StatefulWidget {
  const PremiumFileManagerScreen({super.key});

  @override
  State<PremiumFileManagerScreen> createState() => _PremiumFileManagerScreenState();
}

class _PremiumFileManagerScreenState extends State<PremiumFileManagerScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _listController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _listAnimation;
  
  bool _isGridView = true;
  String _searchQuery = '';
  String _sortBy = 'date';
  String _filterBy = 'all';
  List<String> _selectedFiles = [];
  bool _isSelectionMode = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startEntryAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );

    _listController = AnimationController(
      duration: AppTheme.animationSlow,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: AppTheme.curveEmphasized,
    ));

    _listAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _listController,
      curve: AppTheme.curveEmphasized,
    ));
  }

  void _startEntryAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _listController.forward();
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _listController.dispose();
    super.dispose();
  }

  void _toggleViewMode() {
    setState(() {
      _isGridView = !_isGridView;
    });
    HapticFeedback.lightImpact();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  void _onSortChanged(String sortBy) {
    setState(() {
      _sortBy = sortBy;
    });
    HapticFeedback.lightImpact();
  }

  void _onFilterChanged(String filterBy) {
    setState(() {
      _filterBy = filterBy;
    });
    HapticFeedback.lightImpact();
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedFiles.clear();
      }
    });
    HapticFeedback.lightImpact();
  }

  void _selectFile(String fileId) {
    setState(() {
      if (_selectedFiles.contains(fileId)) {
        _selectedFiles.remove(fileId);
      } else {
        _selectedFiles.add(fileId);
      }
    });
    HapticFeedback.lightImpact();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SafeArea(
              child: Column(
                children: [
                  // Header
                  PremiumFileManagerHeader(
                    isSelectionMode: _isSelectionMode,
                    selectedCount: _selectedFiles.length,
                    onToggleSelection: _toggleSelectionMode,
                    onToggleView: _toggleViewMode,
                    isGridView: _isGridView,
                  ),
                  
                  // Search Bar
                  EnhancedSearchBar(
                    onSearchChanged: _onSearchChanged,
                    hintText: 'Search documents...',
                  ),
                  
                  // Storage Indicator
                  const PremiumStorageIndicator(),
                  
                  // Sort & Filter Bar
                  PremiumSortFilterBar(
                    sortBy: _sortBy,
                    filterBy: _filterBy,
                    onSortChanged: _onSortChanged,
                    onFilterChanged: _onFilterChanged,
                  ),
                  
                  // File List/Grid
                  Expanded(
                    child: AnimatedBuilder(
                      animation: _listAnimation,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, (1 - _listAnimation.value) * 50),
                          child: Opacity(
                            opacity: _listAnimation.value,
                            child: _buildFileList(),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildFileList() {
    final files = _getFilteredFiles();
    
    if (files.isEmpty) {
      return _buildEmptyState();
    }

    if (_isGridView) {
      return _buildGridView(files);
    } else {
      return _buildListView(files);
    }
  }

  Widget _buildGridView(List<FileItem> files) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacing16),
      child: GridView.builder(
        physics: const BouncingScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: AppTheme.spacing16,
          mainAxisSpacing: AppTheme.spacing16,
          childAspectRatio: 0.8,
        ),
        itemCount: files.length,
        itemBuilder: (context, index) {
          final file = files[index];
          return _buildFileCard(file, index);
        },
      ),
    );
  }

  Widget _buildListView(List<FileItem> files) {
    return ListView.builder(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacing16),
      itemCount: files.length,
      itemBuilder: (context, index) {
        final file = files[index];
        return _buildFileCard(file, index);
      },
    );
  }

  Widget _buildFileCard(FileItem file, int index) {
    final isSelected = _selectedFiles.contains(file.id);
    
    return GestureDetector(
      onTap: () {
        if (_isSelectionMode) {
          _selectFile(file.id);
        } else {
          // Open file
          HapticFeedback.lightImpact();
        }
      },
      onLongPress: () {
        if (!_isSelectionMode) {
          _toggleSelectionMode();
          _selectFile(file.id);
        }
      },
      child: Stack(
        children: [
          Container(
            margin: _isGridView 
                ? EdgeInsets.zero 
                : const EdgeInsets.only(bottom: AppTheme.spacing12),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(AppTheme.radius2XL),
              border: Border.all(
                color: isSelected 
                    ? AppTheme.brandPrimary
                    : AppTheme.outlineColor,
                width: isSelected ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
                  blurRadius: AppTheme.shadowBlurLG,
                  offset: const Offset(0, AppTheme.spacing8),
                ),
                if (isSelected)
                  BoxShadow(
                    color: AppTheme.brandPrimary.withValues(alpha: 0.2),
                    blurRadius: AppTheme.shadowBlurLG,
                    offset: const Offset(0, AppTheme.spacing8),
                  ),
              ],
            ),
            child: _isGridView 
                ? _buildGridFileContent(file)
                : _buildListFileContent(file),
          ),
          
          if (_isSelectionMode)
            Positioned(
              top: AppTheme.spacing12,
              right: AppTheme.spacing12,
              child: Container(
                width: AppTheme.spacing24,
                height: AppTheme.spacing24,
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppTheme.brandPrimary
                      : AppTheme.surfaceColor,
                  borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                  border: Border.all(
                    color: isSelected 
                        ? AppTheme.brandPrimary
                        : AppTheme.outlineColor,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? Icon(
                        Icons.check_rounded,
                        color: AppTheme.textOnPrimaryColor,
                        size: AppTheme.spacing16,
                      )
                    : null,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildGridFileContent(FileItem file) {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // File Icon
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: file.gradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(AppTheme.radiusLG),
              ),
              child: Icon(
                file.icon,
                color: AppTheme.textOnPrimaryColor,
                size: AppTheme.spacing40,
              ),
            ),
          ),
          
          const SizedBox(height: AppTheme.spacing12),
          
          // File Info
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  file.name,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: AppTheme.spacing4),
                Text(
                  file.size,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                Text(
                  file.date,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textTertiaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListFileContent(FileItem file) {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacing16),
      child: Row(
        children: [
          Container(
            width: AppTheme.spacing48,
            height: AppTheme.spacing48,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: file.gradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(AppTheme.radiusMD),
            ),
            child: Icon(
              file.icon,
              color: AppTheme.textOnPrimaryColor,
              size: AppTheme.spacing24,
            ),
          ),
          
          const SizedBox(width: AppTheme.spacing16),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  file.name,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: AppTheme.spacing4),
                Row(
                  children: [
                    Text(
                      file.size,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    Text(
                      ' • ',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textTertiaryColor,
                      ),
                    ),
                    Text(
                      file.date,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textTertiaryColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: AppTheme.spacing96,
            height: AppTheme.spacing96,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: AppTheme.primaryGradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(AppTheme.radius3XL),
            ),
            child: Icon(
              Icons.folder_outlined,
              color: AppTheme.textOnPrimaryColor,
              size: AppTheme.spacing48,
            ),
          ),
          
          const SizedBox(height: AppTheme.spacing24),
          
          Text(
            'No Documents Found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          
          const SizedBox(height: AppTheme.spacing8),
          
          Text(
            'Start scanning documents to see them here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: AppTheme.primaryGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radius2XL),
        boxShadow: [
          BoxShadow(
            color: AppTheme.brandPrimary.withValues(alpha: 0.4),
            blurRadius: AppTheme.shadowBlurLG,
            offset: const Offset(0, AppTheme.spacing8),
          ),
        ],
      ),
      child: FloatingActionButton(
        onPressed: () {
          HapticFeedback.mediumImpact();
          Navigator.of(context).pushNamed('/camera');
        },
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: Icon(
          Icons.add_rounded,
          color: AppTheme.textOnPrimaryColor,
          size: AppTheme.spacing28,
        ),
      ),
    );
  }

  List<FileItem> _getFilteredFiles() {
    // Mock data - replace with actual file data
    final allFiles = [
      FileItem(
        id: '1',
        name: 'Invoice_2024_001.pdf',
        size: '2.4 MB',
        date: '2 hours ago',
        type: 'pdf',
        icon: Icons.picture_as_pdf_outlined,
        gradient: [AppTheme.errorColor, AppTheme.errorLightColor],
      ),
      FileItem(
        id: '2',
        name: 'Contract_Draft.pdf',
        size: '1.8 MB',
        date: 'Yesterday',
        type: 'pdf',
        icon: Icons.picture_as_pdf_outlined,
        gradient: [AppTheme.errorColor, AppTheme.errorLightColor],
      ),
      FileItem(
        id: '3',
        name: 'Receipt_Grocery.jpg',
        size: '856 KB',
        date: '3 days ago',
        type: 'image',
        icon: Icons.image_outlined,
        gradient: [AppTheme.secondaryColor, AppTheme.secondaryLightColor],
      ),
    ];

    return allFiles.where((file) {
      final matchesSearch = file.name.toLowerCase().contains(_searchQuery.toLowerCase());
      final matchesFilter = _filterBy == 'all' || file.type == _filterBy;
      return matchesSearch && matchesFilter;
    }).toList();
  }
}

class FileItem {
  final String id;
  final String name;
  final String size;
  final String date;
  final String type;
  final IconData icon;
  final List<Color> gradient;

  FileItem({
    required this.id,
    required this.name,
    required this.size,
    required this.date,
    required this.type,
    required this.icon,
    required this.gradient,
  });
}

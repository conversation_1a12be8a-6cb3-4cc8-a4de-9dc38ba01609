import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';
import '../../../../core/utils/file_utils.dart';
import '../../../../core/constants/app_constants.dart';
import '../../domain/repositories/pdf_repository.dart';

class PdfRepositoryImpl implements PdfRepository {
  @override
  Future<Uint8List> generatePdfFromImages(List<Uint8List> images, String fileName) async {
    try {
      final pdf = pw.Document();
      
      for (final imageBytes in images) {
        final image = pw.MemoryImage(imageBytes);
        
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            margin: const pw.EdgeInsets.all(AppConstants.pdfPageMargin),
            build: (pw.Context context) {
              return pw.Center(
                child: pw.Image(
                  image,
                  fit: pw.BoxFit.contain,
                ),
              );
            },
          ),
        );
      }
      
      return await pdf.save();
    } catch (e) {
      throw Exception('PDF generation failed: $e');
    }
  }

  @override
  Future<String> savePdfToFile(Uint8List pdfBytes, String fileName) async {
    try {
      // Ensure filename has .pdf extension
      String finalFileName = fileName;
      if (!finalFileName.toLowerCase().endsWith(AppConstants.pdfExtension)) {
        finalFileName += AppConstants.pdfExtension;
      }
      
      final file = await FileUtils.savePdfToFile(pdfBytes, finalFileName);
      return file.path;
    } catch (e) {
      throw Exception('Failed to save PDF: $e');
    }
  }

  @override
  Future<void> sharePdf(String filePath) async {
    try {
      await Share.shareXFiles([XFile(filePath)]);
    } catch (e) {
      throw Exception('Failed to share PDF: $e');
    }
  }
}

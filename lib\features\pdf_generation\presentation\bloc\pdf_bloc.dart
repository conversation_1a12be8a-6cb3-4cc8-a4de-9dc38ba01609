import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/generate_pdf.dart';
import '../../data/repositories/pdf_repository_impl.dart';
import 'pdf_event.dart';
import 'pdf_state.dart';

class PdfBloc extends Bloc<PdfEvent, PdfState> {
  final GeneratePdf generatePdf;
  final PdfRepositoryImpl _repository = PdfRepositoryImpl();

  PdfBloc({
    required this.generatePdf,
  }) : super(PdfInitial()) {
    on<GeneratePdfEvent>(_onGeneratePdf);
    on<SharePdfEvent>(_onSharePdf);
  }

  Future<void> _onGeneratePdf(
    GeneratePdfEvent event,
    Emitter<PdfState> emit,
  ) async {
    try {
      emit(PdfGenerating());
      
      final filePath = await generatePdf(event.images, event.fileName);
      
      emit(PdfGenerated(
        filePath: filePath,
        fileName: event.fileName,
      ));
    } catch (e) {
      emit(PdfError(message: e.toString()));
    }
  }

  Future<void> _onSharePdf(
    SharePdfEvent event,
    Emitter<PdfState> emit,
  ) async {
    try {
      emit(PdfSharing());
      
      await _repository.sharePdf(event.filePath);
      
      emit(PdfShared());
    } catch (e) {
      emit(PdfError(message: e.toString()));
    }
  }
}

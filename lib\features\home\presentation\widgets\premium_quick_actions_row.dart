import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumQuickActionsRow extends StatefulWidget {
  const PremiumQuickActionsRow({super.key});

  @override
  State<PremiumQuickActionsRow> createState() => _PremiumQuickActionsRowState();
}

class _PremiumQuickActionsRowState extends State<PremiumQuickActionsRow>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _scaleAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startStaggeredAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _scaleAnimations = List.generate(4, (index) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Interval(
            index * 0.1,
            0.4 + (index * 0.1),
            curve: AppTheme.curveEmphasized,
          ),
        ),
      );
    });
  }

  void _startStaggeredAnimations() {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacing20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Title
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimaryColor,
            ),
          ),

          const SizedBox(height: AppTheme.spacing16),

          // Actions Grid
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  index: 0,
                  icon: Icons.camera_alt_rounded,
                  title: 'Scan Document',
                  subtitle: 'Camera scan',
                  gradient: AppTheme.primaryGradient,
                  onTap: () {
                    HapticFeedback.mediumImpact();
                    Navigator.of(context).pushNamed('/camera');
                  },
                ),
              ),
              const SizedBox(width: AppTheme.spacing12),
              Expanded(
                child: _buildActionCard(
                  index: 1,
                  icon: Icons.photo_library_rounded,
                  title: 'Import Image',
                  subtitle: 'From gallery',
                  gradient: AppTheme.secondaryGradient,
                  onTap: () {
                    HapticFeedback.lightImpact();
                    // Handle import from gallery
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacing12),

          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  index: 2,
                  icon: Icons.folder_open_rounded,
                  title: 'Browse Files',
                  subtitle: 'View documents',
                  gradient: AppTheme.accentGradient,
                  onTap: () {
                    HapticFeedback.lightImpact();
                    Navigator.of(context).pushNamed('/file-manager');
                  },
                ),
              ),
              const SizedBox(width: AppTheme.spacing12),
              Expanded(
                child: _buildActionCard(
                  index: 3,
                  icon: Icons.qr_code_scanner_rounded,
                  title: 'QR Scanner',
                  subtitle: 'Scan QR codes',
                  gradient: [AppTheme.warningColor, AppTheme.warningLightColor],
                  onTap: () {
                    HapticFeedback.lightImpact();
                    // Handle QR scanner
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required int index,
    required IconData icon,
    required String title,
    required String subtitle,
    required List<Color> gradient,
    required VoidCallback onTap,
  }) {
    return AnimatedBuilder(
      animation: _scaleAnimations[index],
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimations[index].value,
          child: GestureDetector(
            onTap: onTap,
            child: Container(
              height: AppTheme.spacing144, // Fixed height with more space
              decoration: BoxDecoration(
                color: AppTheme.surfaceColor,
                borderRadius: BorderRadius.circular(AppTheme.radiusXL),
                border: Border.all(color: AppTheme.outlineColor, width: 1),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.textPrimaryColor.withValues(alpha: 0.06),
                    blurRadius: AppTheme.shadowBlurLG,
                    offset: const Offset(0, AppTheme.spacing8),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacing16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Icon
                    Container(
                      width: AppTheme.spacing40,
                      height: AppTheme.spacing40,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: gradient,
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(AppTheme.radiusMD),
                        boxShadow: [
                          BoxShadow(
                            color: gradient.first.withValues(alpha: 0.3),
                            blurRadius: AppTheme.shadowBlurSM,
                            offset: const Offset(0, AppTheme.spacing4),
                          ),
                        ],
                      ),
                      child: Icon(
                        icon,
                        color: AppTheme.textOnPrimaryColor,
                        size: AppTheme.spacing20,
                      ),
                    ),

                    const SizedBox(height: AppTheme.spacing24),

                    // Title and Subtitle - Simplified structure
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppTheme.spacing2),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

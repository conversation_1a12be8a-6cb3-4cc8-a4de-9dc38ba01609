import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';

class PremiumSortFilterBar extends StatelessWidget {
  final String sortBy;
  final String filterBy;
  final ValueChanged<String> onSortChanged;
  final ValueChanged<String> onFilterChanged;

  const PremiumSortFilterBar({
    super.key,
    required this.sortBy,
    required this.filterBy,
    required this.onSortChanged,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing20,
        vertical: AppTheme.spacing12,
      ),
      child: Row(
        children: [
          Expanded(child: _buildFilterChips()),
          const SizedBox(width: AppTheme.spacing16),
          _buildSortButton(context),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    final filters = [
      {'key': 'all', 'label': 'All', 'icon': Icons.folder_outlined},
      {'key': 'pdf', 'label': 'PDF', 'icon': Icons.picture_as_pdf_outlined},
      {'key': 'image', 'label': 'Images', 'icon': Icons.image_outlined},
    ];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children:
            filters.map((filter) {
              final isSelected = filterBy == filter['key'];
              return Container(
                margin: const EdgeInsets.only(right: AppTheme.spacing8),
                child: GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    onFilterChanged(filter['key'] as String);
                  },
                  child: AnimatedContainer(
                    duration: AppTheme.animationFast,
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacing16,
                      vertical: AppTheme.spacing8,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? AppTheme.brandPrimary.withValues(alpha: 0.1)
                              : AppTheme.surfaceColor,
                      borderRadius: BorderRadius.circular(AppTheme.radiusLG),
                      border: Border.all(
                        color:
                            isSelected
                                ? AppTheme.brandPrimary
                                : AppTheme.outlineColor,
                        width: 1,
                      ),
                      boxShadow: [
                        if (isSelected)
                          BoxShadow(
                            color: AppTheme.brandPrimary.withValues(alpha: 0.2),
                            blurRadius: AppTheme.shadowBlurSM,
                            offset: const Offset(0, AppTheme.spacing2),
                          ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          filter['icon'] as IconData,
                          size: AppTheme.spacing16,
                          color:
                              isSelected
                                  ? AppTheme.brandPrimary
                                  : AppTheme.textSecondaryColor,
                        ),
                        const SizedBox(width: AppTheme.spacing6),
                        Text(
                          filter['label'] as String,
                          style: TextStyle(
                            color:
                                isSelected
                                    ? AppTheme.brandPrimary
                                    : AppTheme.textSecondaryColor,
                            fontWeight:
                                isSelected ? FontWeight.w600 : FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildSortButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showSortOptions(context);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacing12,
          vertical: AppTheme.spacing8,
        ),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(AppTheme.radiusMD),
          border: Border.all(color: AppTheme.outlineColor, width: 1),
          boxShadow: [
            BoxShadow(
              color: AppTheme.textPrimaryColor.withValues(alpha: 0.04),
              blurRadius: AppTheme.shadowBlurSM,
              offset: const Offset(0, AppTheme.spacing2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.sort_rounded,
              size: AppTheme.spacing16,
              color: AppTheme.textSecondaryColor,
            ),
            const SizedBox(width: AppTheme.spacing4),
            Text(
              _getSortLabel(),
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getSortLabel() {
    switch (sortBy) {
      case 'name':
        return 'Name';
      case 'size':
        return 'Size';
      case 'type':
        return 'Type';
      case 'date':
      default:
        return 'Date';
    }
  }

  void _showSortOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppTheme.radius2XL),
                topRight: Radius.circular(AppTheme.radius2XL),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: AppTheme.spacing12),
                  width: AppTheme.spacing40,
                  height: AppTheme.spacing4,
                  decoration: BoxDecoration(
                    color: AppTheme.outlineColor,
                    borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                  ),
                ),

                Padding(
                  padding: const EdgeInsets.all(AppTheme.spacing24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Sort by',
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),

                      const SizedBox(height: AppTheme.spacing16),

                      ...[
                        {
                          'key': 'date',
                          'label': 'Date Modified',
                          'icon': Icons.schedule_rounded,
                        },
                        {
                          'key': 'name',
                          'label': 'Name',
                          'icon': Icons.sort_by_alpha_rounded,
                        },
                        {
                          'key': 'size',
                          'label': 'File Size',
                          'icon': Icons.data_usage_rounded,
                        },
                        {
                          'key': 'type',
                          'label': 'File Type',
                          'icon': Icons.category_rounded,
                        },
                      ].map((option) {
                        final isSelected = sortBy == option['key'];
                        return Container(
                          margin: const EdgeInsets.only(
                            bottom: AppTheme.spacing8,
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(
                                AppTheme.radiusMD,
                              ),
                              onTap: () {
                                HapticFeedback.lightImpact();
                                onSortChanged(option['key'] as String);
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                padding: const EdgeInsets.all(
                                  AppTheme.spacing16,
                                ),
                                decoration: BoxDecoration(
                                  color:
                                      isSelected
                                          ? AppTheme.brandPrimary.withValues(
                                            alpha: 0.1,
                                          )
                                          : Colors.transparent,
                                  borderRadius: BorderRadius.circular(
                                    AppTheme.radiusMD,
                                  ),
                                  border: Border.all(
                                    color:
                                        isSelected
                                            ? AppTheme.brandPrimary.withValues(
                                              alpha: 0.3,
                                            )
                                            : Colors.transparent,
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      option['icon'] as IconData,
                                      color:
                                          isSelected
                                              ? AppTheme.brandPrimary
                                              : AppTheme.textSecondaryColor,
                                      size: AppTheme.spacing20,
                                    ),
                                    const SizedBox(width: AppTheme.spacing16),
                                    Expanded(
                                      child: Text(
                                        option['label'] as String,
                                        style: TextStyle(
                                          color:
                                              isSelected
                                                  ? AppTheme.brandPrimary
                                                  : AppTheme.textPrimaryColor,
                                          fontWeight:
                                              isSelected
                                                  ? FontWeight.w600
                                                  : FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    if (isSelected)
                                      Icon(
                                        Icons.check_rounded,
                                        color: AppTheme.brandPrimary,
                                        size: AppTheme.spacing20,
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }
}

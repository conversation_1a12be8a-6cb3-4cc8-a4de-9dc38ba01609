import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:typed_data';
import '../../../../core/di/injection_container.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/models/scanned_document.dart';
import '../../../pdf_generation/presentation/bloc/pdf_bloc.dart';
import '../../../pdf_generation/presentation/bloc/pdf_event.dart';
import '../../../pdf_generation/presentation/bloc/pdf_state.dart';
import '../widgets/document_page_widget.dart';
import '../widgets/preview_controls.dart';

class DocumentPreviewScreen extends StatefulWidget {
  final List<Uint8List> pages;
  final String? documentName;

  const DocumentPreviewScreen({
    super.key,
    required this.pages,
    this.documentName,
  });

  @override
  State<DocumentPreviewScreen> createState() => _DocumentPreviewScreenState();
}

class _DocumentPreviewScreenState extends State<DocumentPreviewScreen> {
  late PageController _pageController;
  int _currentPageIndex = 0;
  late List<Uint8List> _pages;
  String _documentName = '';

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _pages = List.from(widget.pages);
    _documentName = widget.documentName ?? 'Document_${DateTime.now().millisecondsSinceEpoch}';
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<PdfBloc>(),
      child: Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        appBar: AppBar(
          title: Text(
            'Document Preview',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          actions: [
            IconButton(
              onPressed: () => _showDocumentNameDialog(),
              icon: const Icon(Icons.edit_outlined),
              tooltip: 'Edit document name',
            ),
            IconButton(
              onPressed: () => _addNewPage(),
              icon: const Icon(Icons.add_photo_alternate_outlined),
              tooltip: 'Add page',
            ),
          ],
        ),
        body: BlocConsumer<PdfBloc, PdfState>(
          listener: (context, state) {
            if (state is PdfGenerated) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('PDF saved: ${state.fileName}'),
                  backgroundColor: AppTheme.successColor,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                  ),
                  action: SnackBarAction(
                    label: 'Share',
                    textColor: AppTheme.textOnPrimaryColor,
                    onPressed: () {
                      context.read<PdfBloc>().add(SharePdfEvent(filePath: state.filePath));
                    },
                  ),
                ),
              );
            }
            
            if (state is PdfError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Error: ${state.message}'),
                  backgroundColor: AppTheme.errorColor,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusSM),
                  ),
                ),
              );
            }
          },
          builder: (context, state) {
            return Column(
              children: [
                // Document info header
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacing16),
                  decoration: BoxDecoration(
                    color: AppTheme.surfaceColor,
                    border: Border(
                      bottom: BorderSide(
                        color: AppTheme.outlineColor,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.description_outlined,
                        color: AppTheme.primaryColor,
                        size: AppConstants.iconSizeMD,
                      ),
                      const SizedBox(width: AppTheme.spacing12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _documentName,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              '${_pages.length} page${_pages.length != 1 ? 's' : ''}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppTheme.textSecondaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (_pages.length > 1)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppTheme.spacing12,
                            vertical: AppTheme.spacing4,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                          ),
                          child: Text(
                            '${_currentPageIndex + 1}/${_pages.length}',
                            style: Theme.of(context).textTheme.labelMedium?.copyWith(
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                
                // Page viewer
                Expanded(
                  child: _pages.isEmpty
                      ? _buildEmptyState()
                      : PageView.builder(
                          controller: _pageController,
                          onPageChanged: (index) {
                            setState(() {
                              _currentPageIndex = index;
                            });
                          },
                          itemCount: _pages.length,
                          itemBuilder: (context, index) {
                            return DocumentPageWidget(
                              imageBytes: _pages[index],
                              pageIndex: index,
                              totalPages: _pages.length,
                              onDelete: _pages.length > 1 ? () => _deletePage(index) : null,
                              onReorder: _pages.length > 1 ? (newIndex) => _reorderPage(index, newIndex) : null,
                            );
                          },
                        ),
                ),
                
                // Preview controls
                PreviewControls(
                  onSavePdf: () => _savePdf(context),
                  onAddPage: _addNewPage,
                  onShare: state is PdfGenerated ? () => _sharePdf(context, state.filePath) : null,
                  isGenerating: state is PdfGenerating,
                  canShare: state is PdfGenerated,
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.description_outlined,
            size: AppTheme.spacing64,
            color: AppTheme.textTertiaryColor,
          ),
          const SizedBox(height: AppTheme.spacing16),
          Text(
            'No pages added',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacing8),
          Text(
            'Add pages to create your document',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textTertiaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacing24),
          ElevatedButton.icon(
            onPressed: _addNewPage,
            icon: const Icon(Icons.add_photo_alternate_outlined),
            label: const Text('Add Page'),
          ),
        ],
      ),
    );
  }

  void _showDocumentNameDialog() {
    final controller = TextEditingController(text: _documentName);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Document Name'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Enter document name',
            hintText: 'My Document',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _documentName = controller.text.trim().isNotEmpty 
                    ? controller.text.trim() 
                    : 'Untitled Document';
              });
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _addNewPage() {
    Navigator.of(context).pushNamed('/camera').then((result) {
      if (result is Uint8List) {
        setState(() {
          _pages.add(result);
        });
        // Navigate to the new page
        _pageController.animateToPage(
          _pages.length - 1,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  void _deletePage(int index) {
    if (_pages.length <= 1) return;
    
    setState(() {
      _pages.removeAt(index);
      if (_currentPageIndex >= _pages.length) {
        _currentPageIndex = _pages.length - 1;
      }
    });
    
    if (_currentPageIndex >= 0) {
      _pageController.animateToPage(
        _currentPageIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _reorderPage(int oldIndex, int newIndex) {
    setState(() {
      final page = _pages.removeAt(oldIndex);
      _pages.insert(newIndex, page);
      _currentPageIndex = newIndex;
    });
    
    _pageController.animateToPage(
      newIndex,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _savePdf(BuildContext context) {
    if (_pages.isEmpty) return;
    
    context.read<PdfBloc>().add(
      GeneratePdfEvent(
        images: _pages,
        fileName: _documentName,
      ),
    );
  }

  void _sharePdf(BuildContext context, String filePath) {
    context.read<PdfBloc>().add(SharePdfEvent(filePath: filePath));
  }
}
